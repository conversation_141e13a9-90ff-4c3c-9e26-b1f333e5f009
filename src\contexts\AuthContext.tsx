import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from "react";
import { AuthContextType, User, UserRole } from "@/lib/types/auth";
import { CognitoService } from "@/lib/services/cognito-service";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Get inactivity timeout from environment variables (in minutes, default to 30 minutes)
const getInactivityTimeout = (): number => {
  const envTimeout =
    window?.ENV?.VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES ??
    import.meta.env.VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES;

  const timeoutMinutes = envTimeout ? parseInt(envTimeout, 10) : 30;
  return timeoutMinutes * 60 * 1000; // Convert to milliseconds
};

// Get warning time before logout (in minutes, default to 5 minutes)
const getWarningTime = (): number => {
  const envWarning =
    window?.ENV?.VITE_SESSION_WARNING_TIME_MINUTES ??
    import.meta.env.VITE_SESSION_WARNING_TIME_MINUTES;

  const warningMinutes = envWarning ? parseInt(envWarning, 10) : 5;
  return warningMinutes * 60 * 1000; // Convert to milliseconds
};

// Create auth context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: () => { },
  logout: async () => { },
  hasPermission: () => false,
  handleCallback: async () => false,
});

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showInactivityWarning, setShowInactivityWarning] = useState(false);

  // Refs for timers to avoid stale closures
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Constants
  const INACTIVITY_TIMEOUT = useMemo(() => getInactivityTimeout(), []);
  const WARNING_TIME = useMemo(() => getWarningTime(), []);
  const REFRESH_INTERVAL = 5 * 60 * 1000; // Check token refresh every 5 minutes

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
    if (warningTimerRef.current) {
      clearTimeout(warningTimerRef.current);
      warningTimerRef.current = null;
    }
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
  }, []);

  // Reset activity timer
  const resetActivityTimer = useCallback(() => {
    if (!user) return;

    lastActivityRef.current = Date.now();
    clearTimers();
    setShowInactivityWarning(false);

    // Set warning timer (show warning before logout)
    warningTimerRef.current = setTimeout(() => {
      setShowInactivityWarning(true);
    }, INACTIVITY_TIMEOUT - WARNING_TIME);

    // Set inactivity timer (force logout)
    inactivityTimerRef.current = setTimeout(() => {
      handleInactivityLogout();
    }, INACTIVITY_TIMEOUT);
  }, [user, INACTIVITY_TIMEOUT, WARNING_TIME]);

  // Handle inactivity logout
  const handleInactivityLogout = useCallback(async () => {
    console.log("Session expired due to inactivity");
    clearTimers();
    setShowInactivityWarning(false);

    try {
      await CognitoService.logout();
      setUser(null);
    } catch (error) {
      console.error("Inactivity logout failed:", error);
      // Force clear user state even if logout fails
      setUser(null);
    }
  }, [clearTimers]);

  // Activity event handler
  const handleActivity = useCallback(() => {
    if (user && !showInactivityWarning) {
      resetActivityTimer();
    }
  }, [user, showInactivityWarning, resetActivityTimer]);

  // Setup activity listeners
  useEffect(() => {
    if (!user) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
    };
  }, [user, handleActivity]);

  // Setup token refresh interval
  useEffect(() => {
    if (!user) return;

    const checkAndRefreshToken = async () => {
      try {
        const expiryTime = localStorage.getItem('cognito_token_expiry');
        if (expiryTime) {
          const timeUntilExpiry = parseInt(expiryTime) - Date.now();
          // Refresh if token expires in less than 10 minutes
          if (timeUntilExpiry < 10 * 60 * 1000 && timeUntilExpiry > 0) {
            console.log("Proactively refreshing token");
            await CognitoService.refreshTokens();
          }
        }
      } catch (error) {
        console.error("Token refresh check failed:", error);
      }
    };

    // Initial check
    checkAndRefreshToken();

    // Set up interval
    refreshIntervalRef.current = setInterval(checkAndRefreshToken, REFRESH_INTERVAL);

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [user, REFRESH_INTERVAL]);

  // Initialize activity timer when user logs in
  useEffect(() => {
    if (user) {
      resetActivityTimer();
    } else {
      clearTimers();
      setShowInactivityWarning(false);
    }

    return () => {
      clearTimers();
    };
  }, [user, resetActivityTimer, clearTimers]);

  // Check if user is already logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await CognitoService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error("Failed to get current user:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Memoized login function - redirects to Cognito Hosted UI
  const login = useCallback(() => {
    CognitoService.redirectToLogin();
  }, []);

  // Memoized logout function
  const logout = useCallback(async () => {
    setIsLoading(true);
    clearTimers();
    setShowInactivityWarning(false);

    try {
      await CognitoService.logout();
      setUser(null);
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoading(false);
    }
  }, [clearTimers]);

  // Memoized callback handler
  const handleCallback = useCallback(async (code: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Exchange code for tokens
      const success = await CognitoService.exchangeCodeForTokens(code);

      if (!success) {
        return false;
      }

      // Get user from tokens
      const currentUser = await CognitoService.getCurrentUser();
      setUser(currentUser);

      return !!currentUser;
    } catch (error) {
      console.error("Callback handling failed:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Memoized permission checker
  const hasPermission = useCallback((requiredRole: UserRole): boolean => {
    if (!user) return false;

    // Admin role has access to everything
    if (user.role === "admin") return true;

    // Check if user role matches required role
    return user.role === requiredRole;
  }, [user]);

  // Handle extending session from warning dialog
  const handleExtendSession = useCallback(() => {
    setShowInactivityWarning(false);
    resetActivityTimer();
  }, [resetActivityTimer]);

  // Handle logout from warning dialog
  const handleLogoutFromWarning = useCallback(() => {
    setShowInactivityWarning(false);
    handleInactivityLogout();
  }, [handleInactivityLogout]);

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo<AuthContextType>(() => ({
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    hasPermission,
    handleCallback,
  }), [user, isLoading, login, logout, hasPermission, handleCallback]);

  return (
    <>
      <AuthContext.Provider value={contextValue}>
        {children}
      </AuthContext.Provider>

      {/* Inactivity Warning Dialog */}
      <AlertDialog open={showInactivityWarning} onOpenChange={() => { }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Session Expiring Soon</AlertDialogTitle>
            <AlertDialogDescription>
              Your session will expire due to inactivity in {Math.ceil(WARNING_TIME / 60000)} minutes.
              Would you like to extend your session?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleLogoutFromWarning}>
              Logout Now
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleExtendSession}>
              Extend Session
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
