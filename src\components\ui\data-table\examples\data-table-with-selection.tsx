import { useMemo, useState } from "react";
import { ColumnDef, SortingState, PaginationState } from "@tanstack/react-table";
import { DataTable, useRowSelection, addSelectionColumn } from "../index";
import { Button } from "@/components/ui/button";
import { TableFilter } from "@/lib/services/table-service";

// Example data type
type Person = {
  id: string;
  name: string;
  email: string;
  age: number;
  status: "active" | "inactive";
};

// Example data
const sampleData: Person[] = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", age: 30, status: "active" },
  { id: "2", name: "<PERSON>", email: "<EMAIL>", age: 25, status: "inactive" },
  { id: "3", name: "<PERSON>", email: "<EMAIL>", age: 35, status: "active" },
  { id: "4", name: "<PERSON>", email: "<EMAIL>", age: 28, status: "active" },
  { id: "5", name: "<PERSON>", email: "<EMAIL>", age: 42, status: "inactive" },
];

export function DataTableWithSelectionExample() {
  // Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // Row selection state
  const {
    rowSelection,
    onRowSelectionChange,
    getSelectedRowIds,
    getSelectedRowCount,
    clearSelection,
  } = useRowSelection({
    onSelectionChange: (selection) => {
      console.log("Selection changed:", selection);
    },
  });

  // Define columns
  const baseColumns: ColumnDef<Person>[] = useMemo(
    () => [
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "email",
        header: "Email",
      },
      {
        accessorKey: "age",
        header: "Age",
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: ({ getValue }) => {
          const status = getValue() as string;
          return (
            <span
              className={`px-2 py-1 rounded-full text-xs ${status === "active"
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
                }`}
            >
              {status}
            </span>
          );
        },
      },
    ],
    []
  );

  // Add selection column to the beginning
  const columns = useMemo(() => addSelectionColumn(baseColumns), [baseColumns]);

  // Mock handlers
  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination);
  };

  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting);
  };

  const handleFilterChange = (filter: TableFilter) => {
    console.log("Filter changed:", filter);
  };

  // Get selected rows data
  const selectedRowIds = getSelectedRowIds();
  const selectedData = sampleData.filter(item => selectedRowIds.includes(item.id));

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">DataTable with Row Selection</h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {getSelectedRowCount()} of {sampleData.length} rows selected
          </span>
          {getSelectedRowCount() > 0 && (
            <Button variant="outline" size="sm" onClick={clearSelection}>
              Clear Selection
            </Button>
          )}
        </div>
      </div>

      <DataTable
        columns={columns}
        data={sampleData}
        pageCount={1}
        totalCount={sampleData.length}
        pagination={pagination}
        sorting={sorting}
        onPaginationChange={handlePaginationChange}
        onSortingChange={handleSortingChange}
        onFilterChange={handleFilterChange}
        // Row selection props
        enableRowSelection={true}
        enableMultiRowSelection={true}
        rowSelection={rowSelection}
        onRowSelectionChange={onRowSelectionChange}
        getRowId={(row: any) => row.id}
      />

      {/* Display selected data */}
      {selectedData.length > 0 && (
        <div className="mt-4 p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">Selected Rows:</h3>
          <pre className="text-sm bg-muted p-2 rounded overflow-auto">
            {JSON.stringify(selectedData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
