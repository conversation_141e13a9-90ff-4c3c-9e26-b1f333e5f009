import { useParams } from "react-router-dom";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { useCallback } from "react";
import { FormSchema } from "@/lib/schemas/form-schemas";

// Import custom hooks
import { useFormOperations } from "@/hooks/useFormOperations";
import { useDragAndDrop } from "@/hooks/useDragAndDrop";
import { createLazyComponent } from "@/components/ui/lazy-component";
import LoadingIndicator from "@/components/form-builder/LoadingIndicator";

// Lazy load components for better code splitting
const FormHeader = createLazyComponent(
  () => import("@/components/form-builder/FormHeader")
);

const FormMetaFields = createLazyComponent(
  () => import("@/components/form-builder/FormMetaFields")
);

const FormTabs = createLazyComponent(
  () => import("@/components/form-builder/FormTabs")
);

const DragOverlayWrapper = createLazyComponent(
  () => import("@/components/form-builder/DragOverlayWrapper")
);

export default function FormBuilderPage() {
  const { id } = useParams<{ id: string }>();

  // Use custom hooks
  const {
    form,
    isLoading,
    isSaving,
    isPublishing,
    isNewForm,
    isFormValid,
    handleFormChange,
    handleComponentsChange,
    handleSave,
    handlePublish,
  } = useFormOperations({ id });

  const { activeId, sensors, handleDragStart, handleDragEnd } = useDragAndDrop({
    components: form.components,
    onComponentsChange: handleComponentsChange,
  });

  // Handle schema metadata updates from JSON import/edit
  const handleSchemaMetadataChange = useCallback((metadata: Partial<FormSchema>) => {
    Object.entries(metadata).forEach(([key, value]) => {
      if (key !== 'components') {
        handleFormChange(key as keyof FormSchema, value);
      }
    });
  }, [handleFormChange]);

  if (isLoading) {
    return <LoadingIndicator message="Loading form..." />;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-6">
        <FormHeader
          title={isNewForm ? "Create New Form" : `Edit Form: ${form.name}`}
          isSaving={isSaving}
          isPublishing={isPublishing}
          status={form.status}
          isFormValid={isFormValid}
          onSave={handleSave}
          onPublish={handlePublish}
        />

        <FormMetaFields form={form} onFormChange={handleFormChange} />

        <FormTabs
          schema={form}
          onComponentsChange={handleComponentsChange}
          onSchemaMetadataChange={handleSchemaMetadataChange}
          useDndContext={false} // We're providing our own DndContext
        />
      </div>

      <DragOverlayWrapper activeId={activeId} components={form.components} />
    </DndContext>
  );
}
