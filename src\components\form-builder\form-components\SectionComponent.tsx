import { memo } from "react";
import {
  FormComponent,
  SectionComponent as SectionComponentType,
} from "@/lib/schemas/form-schemas";
import { FolderOpen, AlertCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { getComponentValidationRules } from "@/lib/utils/zod-validation-utils";
import RenderComponent from "./RenderComponent";

interface SectionComponentProps {
  component: FormComponent;
  formProps: {
    watch: any;
    register: any;
    control: any;
    errors: any;
    setValue: any;
  };
  allComponents: FormComponent[];
}

/**
 * Renders a section component in the form preview
 */
function SectionComponent({
  component,
  formProps,
  allComponents,
}: SectionComponentProps) {
  const sectionComponent = component as SectionComponentType;
  const isCollapsible = sectionComponent.collapsible ?? true;

  // Find child components of this section
  const childComponents = allComponents.filter(
    (c) => c.parentId === component.id
  );

  if (isCollapsible) {
    return renderCollapsibleSection(component, {
      childComponents,
      formProps,
      allComponents,
    });
  } else {
    return renderNonCollapsibleSection(component, {
      childComponents,
      formProps,
      allComponents,
    });
  }
}

// Type for section rendering props to reduce parameter count
type SectionRenderProps = {
  childComponents: FormComponent[];
  formProps: {
    watch: any;
    register: any;
    control: any;
    errors: any;
    setValue: any;
  };
  allComponents: FormComponent[];
};

// Helper function to render a collapsible section
function renderCollapsibleSection(
  component: FormComponent,
  props: SectionRenderProps
) {
  const sectionComponent = component as SectionComponentType;
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={sectionComponent.defaultCollapsed ? undefined : "content"}
      className="w-full"
    >
      <AccordionItem value="content" className="border rounded-md">
        <AccordionTrigger className="px-4 py-2 hover:no-underline">
          <div className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5 text-muted-foreground" />
            <div className="text-left">
              <h3 className="font-medium">{component.label}</h3>
              {sectionComponent.description && (
                <p className="text-sm text-muted-foreground">
                  {sectionComponent.description}
                </p>
              )}
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 py-2">
          {renderSectionChildren(props)}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

// Helper function to render a non-collapsible section
function renderNonCollapsibleSection(
  component: FormComponent,
  props: SectionRenderProps
) {
  const sectionComponent = component as SectionComponentType;
  return (
    <div className="border rounded-md p-4">
      <div className="flex items-center gap-2">
        <FolderOpen className="h-5 w-5 text-muted-foreground" />
        <div>
          <h3 className="font-medium">{component.label}</h3>
          {sectionComponent.description && (
            <p className="text-sm text-muted-foreground">
              {sectionComponent.description}
            </p>
          )}
        </div>
      </div>
      <div className="mt-2 border-t pt-2">{renderSectionChildren(props)}</div>
    </div>
  );
}

// Helper function to render child components in a section
function renderSectionChildren({
  childComponents,
  formProps,
  allComponents,
}: SectionRenderProps) {
  const { watch, register, control, errors, setValue } = formProps;

  if (childComponents.length === 0) {
    return (
      <div className="text-sm text-muted-foreground italic mb-2">
        No content in this section
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {childComponents.map((childComponent) => {
        // Check if this component should be rendered based on conditional rules
        const shouldRender = evaluateConditionalRendering(
          childComponent,
          watch
        );

        if (!shouldRender) {
          return null;
        }

        return (
          <div key={childComponent.id} className="space-y-2">
            <div className="space-y-1">
              {childComponent.type !== "section" && (
                <Label
                  htmlFor={childComponent.id}
                  className="flex items-center gap-1"
                >
                  {childComponent.label}
                  {childComponent.required && (
                    <span className="text-destructive">*</span>
                  )}
                </Label>
              )}

              <RenderComponent
                component={childComponent}
                register={register}
                control={control}
                errors={errors}
                setValue={setValue}
                watch={watch}
                validationRules={getComponentValidationRules(childComponent)}
                allComponents={allComponents}
              />

              {errors[childComponent.name] && (
                <div className="text-sm text-destructive flex items-center gap-1 mt-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors[childComponent.name]?.message as string}</span>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default memo(SectionComponent);
