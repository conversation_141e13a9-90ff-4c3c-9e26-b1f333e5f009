import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UseFormReturn } from "react-hook-form";
import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormApplication, FormSubmission } from "@/lib/types/submission";
import { SubmissionService } from "@/lib/services/submission-service";
import { User } from "@/lib/types/auth";
import { processFormData } from "@/lib/utils/form-data-processor";
import { ErrorResponse } from "@/lib/types/api";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { organizeComponentsIntoSteps } from "@/lib/utils/form-structure-utils";
import { getComponentValidationRules } from "@/lib/utils/zod-validation-utils";
import { getCellConfig } from "@/lib/utils/data-grid-utils";
import { indicesToExcel } from "@/lib/utils/grid-utils";

interface UseFormSubmissionOperationsProps {
  form: FormSchema | null;
  projectRef?: string;
  submission: FormSubmission | null;
  methods: UseFormReturn;
  user: User | null;
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  commitCurrentStepData: () => Promise<boolean>;
  setSubmission: (submission: FormSubmission) => void;
}

interface UseFormSubmissionOperationsReturn {
  isSaving: boolean;
  isSubmitting: boolean;
  saveProgress: () => Promise<boolean>;
  submitForm: () => Promise<boolean>;
}

/**
 * Hook for form submission operations (save and submit)
 */
export function useFormSubmissionOperations({
  form,
  submission,
  methods,
  projectRef,
  user,
  setFormStatus,
  commitCurrentStepData,
  setSubmission,
}: UseFormSubmissionOperationsProps): UseFormSubmissionOperationsReturn {
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Get field names for a specific step
   */
  // const getStepFieldNames = (stepComponents: any[]): string[] => {
  //   if (!form) return [];

  //   const fieldNames: string[] = [];
  //   const { watch } = methods;

  //   // Process direct components in the step
  //   stepComponents
  //     .filter(
  //       (component) =>
  //         component.type !== "step" &&
  //         component.type !== "section" &&
  //         component.name && // Ensure component has a name
  //         component.name.trim() !== "" // Ensure name is not empty
  //     )
  //     .forEach((component) => {
  //       // Only include fields that should be rendered (conditional rendering check)
  //       try {
  //         const shouldRender = evaluateConditionalRendering(component, watch);
  //         if (shouldRender) {
  //           fieldNames.push(component.name);
  //         }
  //       } catch (error) {
  //         // If conditional rendering evaluation fails, include the field anyway
  //         console.warn(
  //           `Error evaluating conditional rendering for ${component.name}:`,
  //           error
  //         );
  //         fieldNames.push(component.name);
  //       }
  //     });

  //   // Process section components and their children
  //   stepComponents
  //     .filter((component) => component.type === "section")
  //     .forEach((section) => {
  //       const childComponents = form.components.filter(
  //         (c) => c.parentId === section.id
  //       );

  //       childComponents
  //         .filter(
  //           (child) =>
  //             child.type !== "section" && child.name && child.name.trim() !== ""
  //         )
  //         .forEach((child) => {
  //           try {
  //             const shouldRender = evaluateConditionalRendering(child, watch);
  //             if (shouldRender) {
  //               fieldNames.push(child.name);
  //             }
  //           } catch (error) {
  //             console.warn(
  //               `Error evaluating conditional rendering for ${child.name}:`,
  //               error
  //             );
  //             fieldNames.push(child.name);
  //           }
  //         });
  //     });

  //   return fieldNames;
  // };

  /**
   * Convert structured DataGrid format to flat format for validation
   */
  const convertStructuredToFlat = (
    structuredValue: any
  ): Record<string, any> => {
    const flatValue: Record<string, any> = {};

    if (!structuredValue.rows || !Array.isArray(structuredValue.rows)) {
      return flatValue;
    }

    // Get column headers from metadata
    const columnHeaders = structuredValue.metadata?.columnHeaders || [];

    structuredValue.rows.forEach((row: any, rowIndex: number) => {
      if (row.cells && typeof row.cells === "object") {
        // For each cell in the row
        Object.keys(row.cells).forEach((columnHeader) => {
          const cell = row.cells[columnHeader];

          // Find the column index based on the column header
          const colIndex = columnHeaders.indexOf(columnHeader);

          if (colIndex >= 0) {
            // Convert to Excel-style cell ID (A1, B1, C1, etc.)
            // rowIndex + 1 because we skip the header row (row 0)
            // colIndex + 1 because we skip the header column (col 0)
            const cellId = indicesToExcel(rowIndex + 1, colIndex + 1);
            flatValue[cellId] = cell.value || "";
          }
        });
      }
    });

    return flatValue;
  };

  /**
   * Validate a DataGrid component
   */
  const validateDataGrid = (
    component: any,
    value: any
  ): { isValid: boolean; errors: string[] } => {
    try {
      if (component.type !== "datagrid") {
        return { isValid: true, errors: [] };
      }

      // Normalize the DataGrid value to flat format
      let flatValue: Record<string, any> = {};

      if (!value) {
        flatValue = {};
      } else if (typeof value === "object") {
        // If value is in the new format with flat and structured properties
        if ("flat" in value && "structured" in value) {
          flatValue = value.flat || {};
        } else if ("rows" in value && "metadata" in value) {
          // Convert structured format to flat format for validation

          flatValue = convertStructuredToFlat(value);
        } else {
          // Otherwise, use the value as is (assuming it's already flat)
          flatValue = value;
        }
      }

      const errors: string[] = [];

      // Validate each cell that has validation rules or is required
      for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
        for (let colIndex = 1; colIndex < component.columns; colIndex++) {
          const cellConfig = getCellConfig(component.cells, rowIndex, colIndex);
          const cellId = indicesToExcel(rowIndex, colIndex);

          // Skip if this cell is a header or doesn't exist
          if (!cellConfig || cellConfig.type === "header") {
            continue;
          }

          // Check if this cell has any validation rules or is required
          const hasValidations =
            cellConfig.validations && cellConfig.validations.length > 0;
          const isRequired =
            hasValidations &&
            cellConfig.validations?.some((v) => v.rule === "required");

          const cellValue = flatValue[cellId] || "";

          // Validate the cell using our custom validation logic
          let cellError: string | null = null;

          // Check required validation first
          if (isRequired && (!cellValue || cellValue.trim() === "")) {
            const requiredValidation = cellConfig.validations?.find(
              (v) => v.rule === "required"
            );
            cellError = requiredValidation?.message || "This field is required";
          }

          // If not empty and has other validations, check them
          if (
            !cellError &&
            cellValue &&
            hasValidations &&
            cellConfig.validations
          ) {
            for (const validation of cellConfig.validations) {
              if (validation.rule === "required") {
                // Already checked above
                continue;
              }

              // Check other validation rules
              if (
                validation.rule === "min" &&
                cellConfig.inputType === "number"
              ) {
                const numValue = parseFloat(cellValue);
                if (
                  !isNaN(numValue) &&
                  validation.value &&
                  numValue < validation.value
                ) {
                  cellError =
                    validation.message ||
                    `Minimum value is ${validation.value}`;
                  break;
                }
              }

              if (
                validation.rule === "max" &&
                cellConfig.inputType === "number"
              ) {
                const numValue = parseFloat(cellValue);
                if (
                  !isNaN(numValue) &&
                  validation.value &&
                  numValue > validation.value
                ) {
                  cellError =
                    validation.message ||
                    `Maximum value is ${validation.value}`;
                  break;
                }
              }

              if (
                validation.rule === "minLength" &&
                cellConfig.inputType === "text"
              ) {
                if (validation.value && cellValue.length < validation.value) {
                  cellError =
                    validation.message ||
                    `Minimum length is ${validation.value} characters`;
                  break;
                }
              }

              if (
                validation.rule === "maxLength" &&
                cellConfig.inputType === "text"
              ) {
                if (validation.value && cellValue.length > validation.value) {
                  cellError =
                    validation.message ||
                    `Maximum length is ${validation.value} characters`;
                  break;
                }
              }

              if (
                validation.rule === "pattern" &&
                cellConfig.inputType === "text"
              ) {
                if (validation.value) {
                  const pattern = new RegExp(validation.value);
                  if (!pattern.test(cellValue)) {
                    cellError = validation.message || "Invalid format";
                    break;
                  }
                }
              }
            }
          }

          if (cellError) {
            errors.push(
              `${
                component.label || component.name
              } - Cell ${cellId}: ${cellError}`
            );
          }
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error) {
      console.warn(`Error validating DataGrid ${component.name}:`, error);
      return { isValid: true, errors: [] }; // If validation fails, assume valid to avoid blocking submission
    }
  };

  /**
   * Validate a single field using its validation rules
   */
  const validateField = (
    component: any,
    value: any
  ): { isValid: boolean; error?: string } => {
    try {
      // Handle DataGrid components specially
      if (component.type === "datagrid") {
        const dataGridResult = validateDataGrid(component, value);
        return {
          isValid: dataGridResult.isValid,
          error:
            dataGridResult.errors.length > 0
              ? dataGridResult.errors.join("; ")
              : undefined,
        };
      }

      const validationRules = getComponentValidationRules(component);

      // Check required validation
      if (
        validationRules.required &&
        (value === undefined || value === null || value === "")
      ) {
        return {
          isValid: false,
          error: `${component.label || component.name} is required`,
        };
      }

      // Check pattern validation
      if (validationRules.pattern && value) {
        // Special handling for email validation with fallback
        if (
          validationRules.pattern.message &&
          validationRules.pattern.message.toLowerCase().includes("email")
        ) {
          // Use a more robust email validation
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            return {
              isValid: false,
              error:
                validationRules.pattern.message ||
                "Please enter a valid email address",
            };
          }
        } else {
          // Regular pattern validation
          if (!validationRules.pattern.value.test(value)) {
            return {
              isValid: false,
              error:
                validationRules.pattern.message ||
                `${component.label || component.name} format is invalid`,
            };
          }
        }
      }

      // Check min length validation
      if (
        validationRules.minLength &&
        value &&
        value.length < validationRules.minLength.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.minLength.message ||
            `${component.label || component.name} must be at least ${
              validationRules.minLength.value
            } characters`,
        };
      }

      // Check max length validation
      if (
        validationRules.maxLength &&
        value &&
        value.length > validationRules.maxLength.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.maxLength.message ||
            `${component.label || component.name} must be no more than ${
              validationRules.maxLength.value
            } characters`,
        };
      }

      // Check min value validation
      if (
        validationRules.min &&
        value !== undefined &&
        value !== null &&
        Number(value) < validationRules.min.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.min.message ||
            `${component.label || component.name} must be at least ${
              validationRules.min.value
            }`,
        };
      }

      // Check max value validation
      if (
        validationRules.max &&
        value !== undefined &&
        value !== null &&
        Number(value) > validationRules.max.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.max.message ||
            `${component.label || component.name} must be no more than ${
              validationRules.max.value
            }`,
        };
      }

      return { isValid: true };
    } catch (error) {
      console.warn(`Error validating field ${component.name}:`, error);
      return { isValid: true }; // If validation fails, assume valid to avoid blocking submission
    }
  };

  /**
   * Validate all form steps by checking each step's fields using schema validation
   */
  const validateAllSteps = async (): Promise<{
    isValid: boolean;
    errors: any;
    invalidSteps: number[];
  }> => {
    if (!form) return { isValid: true, errors: {}, invalidSteps: [] };

    // Get all steps from form structure
    const steps = organizeComponentsIntoSteps(form);
    const allErrors: any = {};
    const invalidSteps: number[] = [];

    // Get current form values
    const formValues = methods.getValues();
    const { watch } = methods;

    // Validate each step
    for (let stepIndex = 0; stepIndex < steps.length; stepIndex++) {
      const step = steps[stepIndex];
      let stepHasErrors = false;

      // Get all components for this step (including nested ones)
      const allStepComponents = [...step.components];

      // Add child components from sections
      step.components
        .filter((component) => component.type === "section")
        .forEach((section) => {
          const childComponents = form.components.filter(
            (c) => c.parentId === section.id
          );
          allStepComponents.push(...childComponents);
        });

      // Validate each component in the step
      for (const component of allStepComponents) {
        // Skip non-input components
        if (
          component.type === "step" ||
          component.type === "section" ||
          !component.name
        ) {
          continue;
        }

        // Check if component should be rendered (conditional rendering)
        try {
          const shouldRender = evaluateConditionalRendering(component, watch);
          if (!shouldRender) {
            continue; // Skip validation for hidden components
          }
        } catch (error) {
          console.warn(
            `Error evaluating conditional rendering for ${component.name}:`,
            error
          );
          // Continue with validation if conditional rendering check fails
        }

        // Get the current value for this field
        const fieldValue = formValues[component.name];

        // Validate the field
        const validationResult = validateField(component, fieldValue);

        if (!validationResult.isValid) {
          stepHasErrors = true;
          allErrors[component.name] = {
            type: "manual",
            message: validationResult.error,
          };
        }
      }

      if (stepHasErrors) {
        invalidSteps.push(stepIndex);
      }
    }

    return {
      isValid: invalidSteps.length === 0,
      errors: allErrors,
      invalidSteps,
    };
  };

  /**
   * Save form progress
   */
  const saveProgress = async (): Promise<boolean> => {
    if (!form || !user) {
      console.error("Cannot save progress: No form or user");
      return false;
    }

    // Prevent multiple simultaneous save operations
    if (isSaving) {
      return false;
    }

    setIsSaving(true);

    try {
      // 1. Commit the current step's data to ensure we have the latest values
      await commitCurrentStepData();

      // 2. Get all form data and process it
      const allFormData = methods.getValues();

      const processedData = processFormData(
        allFormData,
        form?.components || []
      );

      let updatedSubmission: FormApplication;

      // 3. If we don't have a submission yet, create one
      if (!submission) {
        const newSubmission = await SubmissionService.createSubmission({
          formId: form.id,
          formSchema: form,
          applicantId: user.id,
          applicant: user,
          status: "draft",
          data: processedData,
          projectRef,
        });

        updatedSubmission = newSubmission;
      } else {
        // 4. Otherwise, update the existing submission
        updatedSubmission = await SubmissionService.updateSubmission(
          form.id,
          submission.id,
          {
            data: processedData,
            status: "DRAFT",
            projectRef,
            updatedAt: new Date().toISOString(),
          }
        );
      }

      // 5. Update local submission state with the updated data
      setSubmission({
        id: updatedSubmission.id,
        formId: updatedSubmission.formId,
        projectRef: updatedSubmission.projectRef,
        data: updatedSubmission.formData,
        status: updatedSubmission.status,
        formSchema: form,
      });

      // 5. Update UI with success message
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Progress saved successfully!",
      });

      return true;
    } catch (error: any) {
      // Handle error when saving progress
      console.error("Error saving form progress:", error);
      const errorMessage =
        (error as ErrorResponse).details ?? "Unknown error occurred";

      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Failed to save progress: ${errorMessage}`,
      });

      return false;
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Submit form
   */
  const submitForm = async (): Promise<boolean> => {
    if (!form || !user) {
      console.error("Cannot submit form: No form or user");
      return false;
    }

    // Prevent multiple simultaneous submissions
    if (isSubmitting) {
      return false;
    }

    setIsSubmitting(true);

    try {
      // 1. Commit the current step's data to ensure we have the latest values
      await commitCurrentStepData();

      // 2. Validate all steps
      const validationResult = await validateAllSteps();

      if (!validationResult.isValid) {
        const errorCount = Object.keys(validationResult.errors).length;
        const invalidStepNumbers = validationResult.invalidSteps.map(
          (i) => i + 1
        );

        // Set the validation errors in the form state so they can be displayed
        Object.keys(validationResult.errors).forEach((fieldName) => {
          methods.setError(fieldName, validationResult.errors[fieldName]);
        });

        setFormStatus({
          isSubmitted: true,
          isValid: false,
          message: `Please fix ${errorCount} validation error${
            errorCount > 1 ? "s" : ""
          } before submitting. Check step${
            invalidStepNumbers.length > 1 ? "s" : ""
          }: ${invalidStepNumbers.join(", ")}.`,
        });
        return false;
      }

      // 3. Show submitting status
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Submitting form...",
      });

      // 4. Get and process all form data
      const allFormData = methods.getValues();

      const processedData = processFormData(
        allFormData,
        form?.components || []
      );

      let updatedSubmission: FormApplication;

      // 5. If we don't have a submission yet, create one and submit it
      if (!submission) {
        const newSubmission = await SubmissionService.createSubmission({
          formId: form.id,
          formSchema: form,
          applicantId: user.id,
          applicant: user,
          status: "SUBMITTED", // Create it as submitted directly
          data: processedData,
          projectRef,
          submittedAt: new Date().toISOString(),
        });

        updatedSubmission = newSubmission;
      } else {
        // 6. Otherwise, update the existing submission to submitted status
        updatedSubmission = await SubmissionService.updateSubmission(
          form.id,
          submission.id,
          {
            status: "SUBMITTED",
            data: processedData,
            projectRef,
            updatedAt: new Date().toISOString(),
            submittedAt: new Date().toISOString(),
          }
        );
      }

      // 7. Update local submission state
      setSubmission({
        id: updatedSubmission.id,
        formId: updatedSubmission.formId,
        projectRef: updatedSubmission.projectRef,
        data: updatedSubmission.formData,
        status: updatedSubmission.status,
        formSchema: form,
      });

      // 7. Update UI with success message
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Form submitted successfully!",
      });

      // 8. Navigate to applications page after a short delay
      setTimeout(() => {
        navigate("/projects");
      }, 2000);

      return true;
    } catch (error) {
      // Handle error when submitting form
      console.error("Error submitting form:", error);
      const errorMessage =
        (error as ErrorResponse).details ?? "Unknown error occurred";

      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Failed to submit form: ${errorMessage}`,
      });

      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSaving,
    isSubmitting,
    saveProgress,
    submitForm,
  };
}
