// Environment variables template for runtime injection
// This file will be replaced by docker-entrypoint.sh with actual values

window.ENV = {
  VITE_API_MODE: "mock",
  VITE_API_BASE_URL: "http://**********:8080/api/v1",
  VITE_COGNITO_HOSTED_UI_URL: "",
  VITE_COGNITO_CLIENT_ID: "",
  VITE_COGNITO_USER_POOL_ID: "",
  VITE_COGNITO_REGION: "",
  VITE_COGNITO_REDIRECT_URI: "",
  VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES: "30",
  VITE_SESSION_WARNING_TIME_MINUTES: "5",
};
