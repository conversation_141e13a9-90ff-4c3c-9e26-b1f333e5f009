# DataTable with Row Selection

This enhanced DataTable component now supports row selection functionality using TanStack Table's built-in row selection features.

## Features

- ✅ Single and multi-row selection
- ✅ Select all/none functionality
- ✅ Conditional row selection
- ✅ Custom row IDs
- ✅ Selection state management
- ✅ Visual feedback for selected rows
- ✅ Accessible checkboxes

## Basic Usage

### 1. Import Required Components

```tsx
import { DataTable, useRowSelection, addSelectionColumn } from "@/components/ui/data-table";
```

### 2. Set Up Row Selection State

```tsx
const {
  rowSelection,
  onRowSelectionChange,
  getSelectedRowIds,
  getSelectedRowCount,
  clearSelection,
} = useRowSelection({
  onSelectionChange: (selection) => {
    console.log("Selection changed:", selection);
  },
});
```

### 3. Add Selection Column to Your Columns

```tsx
const baseColumns: ColumnDef<YourDataType>[] = [
  // your existing columns
];

// Add selection column at the beginning
const columns = useMemo(() => addSelectionColumn(baseColumns), [baseColumns]);
```

### 4. Configure DataTable with Row Selection

```tsx
<DataTable
  columns={columns}
  data={data}
  // ... other props
  enableRowSelection={true}
  enableMultiRowSelection={true}
  rowSelection={rowSelection}
  onRowSelectionChange={onRowSelectionChange}
  getRowId={(row) => row.id} // Use unique identifier
/>
```

## Advanced Usage

### Conditional Row Selection

```tsx
<DataTable
  // ... other props
  enableRowSelection={(row) => row.original.status === 'active'}
  // Only allow selection of active rows
/>
```

### Single Row Selection

```tsx
<DataTable
  // ... other props
  enableRowSelection={true}
  enableMultiRowSelection={false}
  // Only one row can be selected at a time
/>
```

### Custom Selection Column

Instead of using `addSelectionColumn`, you can create a custom selection column:

```tsx
import { createSelectionColumn } from "@/components/ui/data-table";

const customSelectionColumn = {
  ...createSelectionColumn(),
  header: "Select",
  meta: {
    enablePinning: true,
    width: 60,
  },
};

const columns = [customSelectionColumn, ...baseColumns];
```

## API Reference

### DataTable Props (Row Selection)

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `enableRowSelection` | `boolean \| ((row: Row) => boolean)` | `false` | Enable row selection globally or conditionally |
| `enableMultiRowSelection` | `boolean` | `true` | Allow multiple rows to be selected |
| `rowSelection` | `RowSelectionState` | `{}` | Current row selection state |
| `onRowSelectionChange` | `OnChangeFn<RowSelectionState>` | - | Callback when selection changes |
| `getRowId` | `(row: TData, index: number) => string` | - | Function to get unique row ID |

### useRowSelection Hook

```tsx
const {
  rowSelection,           // Current selection state
  onRowSelectionChange,   // Handler for TanStack Table
  getSelectedRowIds,      // Get array of selected row IDs
  getSelectedRowCount,    // Get count of selected rows
  clearSelection,         // Clear all selections
  selectAll,             // Select all provided row IDs
  isRowSelected,         // Check if specific row is selected
} = useRowSelection(options);
```

### Utility Functions

- `createSelectionColumn<TData>()` - Creates a selection column definition
- `addSelectionColumn(columns)` - Adds selection column to existing columns array

## Styling

Selected rows automatically receive the `data-state="selected"` attribute and are styled using the existing table styles:

```css
tr[data-state="selected"] {
  background-color: var(--muted);
}
```

## Examples

See `src/components/ui/data-table/examples/data-table-with-selection.tsx` for a complete working example.

## TypeScript Support

All components and hooks are fully typed with TypeScript generics to ensure type safety with your data structures.
