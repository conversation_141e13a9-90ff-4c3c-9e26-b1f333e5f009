#!/bin/sh

# Docker entrypoint script for injecting environment variables at runtime

# Function to replace environment variables in JavaScript files
inject_env_vars() {
    # Create the env.js file with runtime environment variables
    cat > /usr/share/nginx/html/env.js << EOF
// Environment variables injected at runtime by Docker
window.ENV = {
  VITE_API_MODE: "${VITE_API_MODE:-real}",
  VITE_API_BASE_URL: "${VITE_API_BASE_URL:-http://**********:8080/api/v1}",
  VITE_COGNITO_HOSTED_UI_URL: "${VITE_COGNITO_HOSTED_UI_URL:-}",
  VITE_COGNITO_CLIENT_ID: "${VITE_COGNITO_CLIENT_ID:-}",
  VITE_COGNITO_USER_POOL_ID: "${VITE_COGNITO_USER_POOL_ID:-}",
  VITE_COGNITO_REGION: "${VITE_COGNITO_REGION:-}",
  VITE_COGNITO_REDIRECT_URI: "${VITE_COGNITO_REDIRECT_URI:-http://localhost:3000/callback}",
  VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES: "${VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES:-30}",
  VITE_SESSION_WARNING_TIME_MINUTES: "${VITE_SESSION_WARNING_TIME_MINUTES:-5}"
};
EOF

    echo "Environment variables injected into /usr/share/nginx/html/env.js"
}

# Inject environment variables
inject_env_vars

# Execute the main command
exec "$@"
