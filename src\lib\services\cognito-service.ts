import { User, UserRole } from "../types/auth";

// Get environment variables with runtime fallback support
const COGNITO_HOSTED_UI_URL =
  window?.ENV?.VITE_COGNITO_HOSTED_UI_URL ??
  import.meta.env.VITE_COGNITO_HOSTED_UI_URL;
const COGNITO_CLIENT_ID =
  window?.ENV?.VITE_COGNITO_CLIENT_ID ?? import.meta.env.VITE_COGNITO_CLIENT_ID;
const COGNITO_REDIRECT_URI =
  window?.ENV?.VITE_COGNITO_REDIRECT_URI ??
  import.meta.env.VITE_COGNITO_REDIRECT_URI;

// Token storage keys
const ID_TOKEN_KEY = "cognito_id_token";
const ACCESS_TOKEN_KEY = "cognito_access_token";
const REFRESH_TOKEN_KEY = "cognito_refresh_token";
const TOKEN_EXPIRY_KEY = "cognito_token_expiry";

// Flags to prevent multiple simultaneous operations
let isRefreshing = false;
let isExchangingCode = false;
let lastProcessedCode = "";

// Token response interface
interface TokenResponse {
  id_token: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

/**
 * Cognito Authentication Service
 * Handles authentication with AWS Cognito
 */
export const CognitoService = {
  /**
   * Redirect to Cognito Hosted UI for authentication
   * @param force If true, forces a redirect even if the flag is set
   */
  redirectToLogin: (force: boolean = false): void => {
    // Clear any existing redirect flag that might be stuck
    if (force) {
      sessionStorage.removeItem("cognito_redirecting");
    }

    // Check if we're already in the process of redirecting
    if (sessionStorage.getItem("cognito_redirecting")) {
      return;
    }

    // Set a flag to prevent multiple redirects
    sessionStorage.setItem("cognito_redirecting", "true");

    // Clear the flag after a short delay (in case the redirect fails)
    setTimeout(() => {
      sessionStorage.removeItem("cognito_redirecting");
    }, 5000);

    const loginUrl = new URL(`${COGNITO_HOSTED_UI_URL}/login`);
    loginUrl.searchParams.append("client_id", COGNITO_CLIENT_ID);
    loginUrl.searchParams.append("response_type", "code");
    loginUrl.searchParams.append("redirect_uri", COGNITO_REDIRECT_URI);

    // Add a timestamp to prevent caching issues
    loginUrl.searchParams.append("state", Date.now().toString());

    // Redirect to Cognito login page
    window.location.href = loginUrl.toString();
  },

  /**
   * Exchange authorization code for tokens
   * @param code Authorization code from Cognito
   * @returns True if exchange successful, false otherwise
   */
  exchangeCodeForTokens: async (code: string): Promise<boolean> => {
    // Check if we've already processed this code
    if (code === lastProcessedCode) {
      // If we have tokens, consider it a success
      const idToken = localStorage.getItem(ID_TOKEN_KEY);
      if (idToken) {
        return true;
      }

      return false;
    }

    // Check if we're already exchanging a code
    if (isExchangingCode) {
      // Wait for the current exchange to complete (max 5 seconds)
      for (let i = 0; i < 50; i++) {
        await new Promise((resolve) => setTimeout(resolve, 100));

        // If no longer exchanging, check if we have tokens
        if (!isExchangingCode) {
          const idToken = localStorage.getItem(ID_TOKEN_KEY);
          if (idToken) {
            lastProcessedCode = code;
            return true;
          }
          break;
        }
      }

      // If still exchanging after timeout, return false
      if (isExchangingCode) {
        console.error("Code exchange timeout");
        return false;
      }
    }

    // Set exchanging flag
    isExchangingCode = true;

    try {
      const tokenEndpoint = `${COGNITO_HOSTED_UI_URL}/oauth2/token`;

      const params = new URLSearchParams();
      params.append("grant_type", "authorization_code");
      params.append("client_id", COGNITO_CLIENT_ID);
      params.append("code", code);
      params.append("redirect_uri", COGNITO_REDIRECT_URI);

      const response = await fetch(tokenEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: params.toString(),
      });

      if (!response.ok) {
        console.error(
          "Failed to exchange code for tokens:",
          await response.text()
        );
        return false;
      }

      const tokens: TokenResponse = await response.json();

      // Store tokens in localStorage
      localStorage.setItem(ID_TOKEN_KEY, tokens.id_token);
      localStorage.setItem(ACCESS_TOKEN_KEY, tokens.access_token);
      localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token);

      // Calculate token expiry time
      const expiryTime = Date.now() + tokens.expires_in * 1000;
      localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());

      // Store the processed code
      lastProcessedCode = code;

      return true;
    } catch (error) {
      console.error("Error exchanging code for tokens:", error);
      return false;
    } finally {
      // Reset exchanging flag
      isExchangingCode = false;
    }
  },

  /**
   * Get current user from ID token
   * @returns User object if found, null otherwise
   */
  getCurrentUser: async (): Promise<User | null> => {
    try {
      const idToken = localStorage.getItem(ID_TOKEN_KEY);

      if (!idToken) {
        return null;
      }

      // Check if token is expired or will expire soon (within 5 minutes)
      const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
      if (expiryTime) {
        const timeUntilExpiry = parseInt(expiryTime) - Date.now();
        if (timeUntilExpiry <= 0) {
          // Token is expired, try to refresh
          console.log("Token expired, attempting refresh");
          const refreshed = await CognitoService.refreshTokens();
          if (!refreshed) {
            // Failed to refresh, clear tokens and return null
            console.log("Token refresh failed, clearing tokens");
            CognitoService.clearTokens();
            return null;
          }
          // Get the updated token after refresh
          const newIdToken = localStorage.getItem(ID_TOKEN_KEY);
          if (!newIdToken) {
            console.error("No ID token after refresh");
            return null;
          }
          // Update idToken for parsing
          const payload = CognitoService.parseJwt(newIdToken);
          if (!payload) {
            return null;
          }

          // Extract user info from refreshed token payload
          const user: User = {
            id: payload.sub,
            email: payload.email,
            name: payload.name ?? payload.email,
            role: CognitoService.determineUserRole(payload),
          };

          return user;
        } else if (timeUntilExpiry < 5 * 60 * 1000) {
          // Token expires in less than 5 minutes, proactively refresh
          console.log("Token expires soon, proactively refreshing");
          CognitoService.refreshTokens().catch((error) => {
            console.error("Proactive token refresh failed:", error);
          });
        }
      }

      // Parse user info from ID token
      const payload = CognitoService.parseJwt(idToken);

      if (!payload) {
        return null;
      }

      // Extract user info from token payload
      // Note: This is a simplified example, adjust based on your Cognito user pool attributes
      const user: User = {
        id: payload.sub,
        email: payload.email,
        name: payload.name ?? payload.email,
        // Determine role based on Cognito groups or custom attributes
        role: CognitoService.determineUserRole(payload),
      };

      return user;
    } catch (error) {
      console.error("Error getting current user:", error);
      return null;
    }
  },

  /**
   * Refresh tokens using refresh token with retry logic
   * @param retryCount Number of retry attempts (default: 0)
   * @returns True if refresh successful, false otherwise
   */
  refreshTokens: async (retryCount: number = 0): Promise<boolean> => {
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000; // 1 second

    // If already refreshing, wait for the current refresh to complete
    if (isRefreshing) {
      // Wait for the current refresh to complete (max 10 seconds)
      for (let i = 0; i < 100; i++) {
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Check if refresh completed successfully
        const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
        if (expiryTime && Date.now() < parseInt(expiryTime)) {
          return true;
        }

        // If no longer refreshing, but still expired, continue with a new refresh
        if (!isRefreshing) {
          break;
        }
      }

      // If still refreshing after timeout, return false
      if (isRefreshing) {
        console.error("Token refresh timeout");
        return false;
      }
    }

    // Set refreshing flag
    isRefreshing = true;

    try {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);

      if (!refreshToken) {
        isRefreshing = false;
        return false;
      }

      const tokenEndpoint = `${COGNITO_HOSTED_UI_URL}/oauth2/token`;

      const params = new URLSearchParams();
      params.append("grant_type", "refresh_token");
      params.append("client_id", COGNITO_CLIENT_ID);
      params.append("refresh_token", refreshToken);

      const response = await fetch(tokenEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: params.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Failed to refresh tokens:", errorText);

        // If this is a 401/403 error, don't retry - tokens are invalid
        if (response.status === 401 || response.status === 403) {
          console.log("Refresh token is invalid, clearing tokens");
          CognitoService.clearTokens();
          isRefreshing = false;
          return false;
        }

        // For other errors, retry if we haven't exceeded max retries
        if (retryCount < MAX_RETRIES) {
          console.log(
            `Retrying token refresh (attempt ${retryCount + 1}/${MAX_RETRIES})`
          );
          isRefreshing = false;
          await new Promise((resolve) =>
            setTimeout(resolve, RETRY_DELAY * (retryCount + 1))
          );
          return CognitoService.refreshTokens(retryCount + 1);
        }

        isRefreshing = false;
        return false;
      }

      const tokens: TokenResponse = await response.json();

      // Validate token response
      if (!tokens.id_token || !tokens.access_token) {
        console.error("Invalid token response: missing required tokens");
        isRefreshing = false;
        return false;
      }

      // Store new tokens in localStorage
      localStorage.setItem(ID_TOKEN_KEY, tokens.id_token);
      localStorage.setItem(ACCESS_TOKEN_KEY, tokens.access_token);

      // Update refresh token if provided (some implementations return a new refresh token)
      if (tokens.refresh_token) {
        localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refresh_token);
      }

      // Calculate token expiry time with a small buffer (subtract 30 seconds)
      const expiryTime = Date.now() + (tokens.expires_in - 30) * 1000;
      localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());

      console.log("Tokens refreshed successfully");
      return true;
    } catch (error) {
      console.error("Error refreshing tokens:", error);

      // Retry on network errors if we haven't exceeded max retries
      if (retryCount < MAX_RETRIES) {
        console.log(
          `Retrying token refresh due to error (attempt ${
            retryCount + 1
          }/${MAX_RETRIES})`
        );
        isRefreshing = false;
        await new Promise((resolve) =>
          setTimeout(resolve, RETRY_DELAY * (retryCount + 1))
        );
        return CognitoService.refreshTokens(retryCount + 1);
      }

      return false;
    } finally {
      // Reset refreshing flag
      isRefreshing = false;
    }
  },

  /**
   * Logout user by clearing tokens and redirecting to Cognito logout
   */
  logout: async (): Promise<void> => {
    // Clear tokens from localStorage
    CognitoService.clearTokens();

    // Redirect to Cognito logout
    const logoutUrl = new URL(`${COGNITO_HOSTED_UI_URL}/logout`);
    logoutUrl.searchParams.append("client_id", COGNITO_CLIENT_ID);
    logoutUrl.searchParams.append("logout_uri", window.location.origin);

    window.location.href = logoutUrl.toString();
  },

  /**
   * Clear tokens from localStorage
   */
  clearTokens: (): void => {
    localStorage.removeItem(ID_TOKEN_KEY);
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  },

  /**
   * Parse JWT token
   * @param token JWT token
   * @returns Parsed token payload or null if invalid
   */
  parseJwt: (token: string): any => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
          .join("")
      );

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error parsing JWT:", error);
      return null;
    }
  },

  /**
   * Determine user role from token payload
   * @param payload Token payload
   * @returns User role
   */
  determineUserRole: (payload: any): UserRole => {
    if (
      payload.role === "PLATEFORM_ADMIN" ||
      payload.role === "SCHEME_ADMIN" ||
      payload.role === "SCHEME_ADMIN_JUNIOR" ||
      payload.role === "admin" ||
      payload.role === "Scheme Admin" ||
      payload.role === "Scheme Admin Junior" ||
      payload.role === "Platform Admin"
    ) {
      return "admin";
    } else {
      return "applicant";
    }
  },
};
