import { useEffect, useState } from "react";
import { FormProvider } from "react-hook-form";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";
import {
  FormPageConfig,
  convertToFormComponents,
} from "@/lib/types/page-config";
import { useDynamicForm } from "@/hooks/useDynamicForm";
import { EntityService } from "@/lib/services/entity-service";
import DynamicPageHeader from "./DynamicPageHeader";
import RenderComponent from "@/components/form-builder/form-components/RenderComponent";
import FormStatusMessage from "@/components/form-builder/form-components/FormStatusMessage";
import StepProgress from "@/components/form-builder/form-components/StepProgress";
import StepNavigation from "@/components/form-builder/form-components/StepNavigation";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { getComponentValidationRules } from "@/lib/utils/zod-validation-utils";
import { useFieldDependencies } from "@/hooks/useFieldDependencies";
import {
  formatDateForInput,
  formatDateTimeForInput,
} from "@/lib/utils/date-utils";
import { Loading } from "@/components/ui/loading";

interface DynamicFormPageProps {
  config: FormPageConfig;
  entityId?: string;
  initialData?: Record<string, any>;
  onSuccess?: (data: Record<string, any>) => void;
  onCancel?: () => void;
  backButtonUrl?: string;
}

/**
 * Component for rendering a dynamic form page
 */
export function DynamicFormPage({
  config,
  entityId,
  initialData: providedInitialData,
  onSuccess,
  onCancel,
}: DynamicFormPageProps) {
  const [initialData, setInitialData] = useState<Record<string, any>>(
    providedInitialData || {}
  );
  const [isLoadingData, setIsLoadingData] = useState(
    !!entityId && !providedInitialData
  );
  const [formComponents, setFormComponents] = useState<any[]>([]);
  // Use dynamic form hook
  const {
    methods,
    isSubmitting,
    formStatus,
    onSubmit,
    onCancel: handleCancel,
    steps,
    isMultiStep,
    currentStep,
    nextStep,
    prevStep,
  } = useDynamicForm({
    config,
    entityId,
    initialData,
    onSuccess,
    onError: (error) => console.error("Form submission error:", error),
  });
  const currentStepData = steps[currentStep];
  // Load entity data if entityId is provided and no initialData was passed
  useEffect(() => {
    const loadEntityData = async () => {
      // Skip loading if initialData was provided via props
      if (providedInitialData) {
        setInitialData(providedInitialData);
        setIsLoadingData(false);
        return;
      }

      if (entityId) {
        try {
          setIsLoadingData(true);
          const data = await EntityService.getEntityById(
            config.entityName,
            config.endpoints.get,
            entityId
          );
          if (data) {
            setInitialData(data);
          } else {
            console.warn(
              `No data found for ${config.entityName} with ID: ${entityId}`
            );
          }
        } catch (error) {
          console.error("Error loading entity data:", error);
        } finally {
          setIsLoadingData(false);
        }
      } else {
        // Reset initial data when creating a new entity
        setInitialData({});
      }
    };

    loadEntityData();
  }, [config.entityName, entityId, providedInitialData]);

  // Validate that the config has fields
  if (
    !config.fields ||
    !Array.isArray(config.fields) ||
    config.fields.length === 0
  ) {
    console.error("Invalid form configuration: No fields defined");
  }

  useEffect(() => {
    const loadFields = async () => {
      const formValues = methods.getValues();
      const components = await convertToFormComponents(currentStepData.fields, initialData, formValues);
      setFormComponents(components);
    };

    loadFields();
  }, [currentStepData.fields, methods]);



  // Extract methods from react-hook-form
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = methods;

  // Initialize field dependencies management
  useFieldDependencies(
    currentStepData.fields,
    methods,
    initialData
  );

  // Watch for role changes specifically to update userId options
  const roleValue = watch("role");
  useEffect(() => {
    if (roleValue) {
      console.log("Role changed to:", roleValue);
      // Force reload of components when role changes
      const loadFields = async () => {
        const formValues = methods.getValues();
        const components = await convertToFormComponents(currentStepData.fields, initialData, formValues);
        setFormComponents(components);
      };
      loadFields();
    }
  }, [roleValue, currentStepData.fields, initialData, methods]);

  // Reload components when form values change for fields with dependencies
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const subscription = watch((formValues, { name }) => {
      // Only reload if a field that has dependents changed
      if (name) {
        const fieldHasDependents = currentStepData.fields.some(field =>
          field.dynamicOptionsConfig?.dependsOn?.includes(name) ||
          field.autoFillConfig?.triggers?.includes(name)
        );

        if (fieldHasDependents) {
          // Clear previous timeout
          if (timeoutId) {
            clearTimeout(timeoutId);
          }

          // Debounce the reload to avoid excessive re-renders
          timeoutId = setTimeout(async () => {
            console.log(`Reloading components due to change in field: ${name}`);
            const components = await convertToFormComponents(currentStepData.fields, initialData, formValues);
            setFormComponents(components);
          }, 300);
        }
      }
    });

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription.unsubscribe();
    };
  }, [watch, currentStepData.fields, initialData]);

  // Reset form when initialData changes
  useEffect(() => {
    if (Object.keys(initialData).length > 0) {
      // Create a copy of initialData to modify date fields
      const formattedData = { ...initialData };

      // Format date and datetime fields for input elements
      config.fields.forEach((field) => {
        if (field.type === "date" && formattedData[field.name]) {
          // Convert ISO date string to YYYY-MM-DD format for date inputs
          formattedData[field.name] = formatDateForInput(
            formattedData[field.name]
          );
        } else if (field.type === "datetime" && formattedData[field.name]) {
          // Convert ISO date string to YYYY-MM-DDThh:mm format for datetime-local inputs
          formattedData[field.name] = formatDateTimeForInput(
            formattedData[field.name]
          );
        }
      });

      // First reset the form with all formatted data
      reset(formattedData);

      // Then explicitly set each field value to ensure they're properly applied
      // This is especially important for select fields
      Object.entries(formattedData).forEach(([key, value]) => {
        if (value !== undefined) {
          setValue(key, value);
        }
      });
    }
  }, [initialData, reset, setValue, config.fields]);



  // Handle cancel button click
  const handleCancelClick = () => {
    if (onCancel) {
      onCancel();
    } else {
      handleCancel();
    }
  };

  if (isLoadingData) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      <DynamicPageHeader
        config={config}
        showBackButton={false}
      // backButtonUrl={backButtonUrl}
      // backButtonText="Back"
      />

      <Card>
        {isMultiStep && (
          <StepProgress
            currentStep={currentStep}
            totalSteps={steps.length}
            steps={steps.map((step) => ({
              label: step.label,
              description: step.description,
            }))}
          />
        )}

        <CardContent className="pt-6">
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <FormStatusMessage
                isSubmitted={formStatus.isSubmitted}
                isValid={formStatus.isValid}
                message={formStatus.message}
              />

              {formComponents.map((component) => {
                // Check if this component should be rendered based on conditional rules
                const shouldRender = evaluateConditionalRendering(
                  component,
                  watch
                );

                if (!shouldRender) {
                  return null; // Skip rendering this component
                }

                return (
                  <div key={component.id} className="space-y-2">
                    <div className="space-y-1">
                      {component.type !== "step" &&
                        component.type !== "section" &&
                        component.type !== "infoText" && (component.defaultValue || !component.disabled) && (
                          <Label
                            htmlFor={component.id}
                            className="flex items-center gap-1"
                          >
                            {component.label}
                            {component.required && (
                              <span className="text-destructive">*</span>
                            )}
                          </Label>
                        )}

                      <RenderComponent
                        key={
                          component.type === "select"
                            ? `${component.name}-${watch(component.name)}-${JSON.stringify(component.options)}`
                            : component.id
                        }
                        component={{
                          ...component,
                          //defaultValue: watch(component.name),
                        }}
                        register={register}
                        control={control}
                        errors={errors}
                        setValue={setValue}
                        watch={watch}
                        validationRules={getComponentValidationRules(component)}
                        allComponents={formComponents}
                        mode="edit"
                      />

                      {errors[component.name] && (
                        <div className="text-sm text-destructive flex items-center gap-1 mt-1">
                          <AlertCircle className="h-4 w-4" />
                          <span>
                            {errors[component.name]?.message as string}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}

              {isMultiStep ? (
                <StepNavigation
                  currentStep={currentStep}
                  totalSteps={steps.length}
                  onNext={nextStep}
                  onPrevious={prevStep}
                  isMultiStep={isMultiStep}
                  onSubmit={handleSubmit(onSubmit)}
                />
              ) : (
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancelClick}
                    disabled={isSubmitting}
                  >
                    {config.cancelButtonText || "Cancel"}
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {config.submitButtonText ||
                      (entityId ? "Update" : "Create")}
                  </Button>
                </div>
              )}
            </form>
          </FormProvider>
        </CardContent>

        <CardFooter className="border-t px-6 py-4 bg-muted/50">
          <p className="text-sm text-muted-foreground">
            {entityId
              ? `Editing ${config.entityName} with ID: ${entityId}`
              : `Creating a new ${config.entityName}`}
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}

export default DynamicFormPage;
