import { apiClient } from "../api/api-client";

/**
 * Document type definition
 */
export interface Document {
  id: string;
  fileName: string;
  parentObjectId: string;
  parentObjectType: string;
  fileType: string;
  isActive: boolean;
  projectRef: string;
  tag: string;
  createdBy: string;
  createdAt: string;
}

/**
 * Service for handling document operations
 */
export const DocumentService = {
  /**
   * Download a single document by ID
   */
  downloadDocument: async (documentId: string): Promise<void> => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/documents/${documentId}/download`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("cognito_id_token")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
      }

      // Get filename from response headers or use a default
      const contentDisposition = response.headers.get("content-disposition");
      let filename = `document-${documentId}`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        );
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, "");
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading document:", error);
      throw error;
    }
  },

  /**
   * Download multiple documents as a ZIP file
   */
  downloadMultipleDocuments: async (
    documentIds: string[],
    zipFileName?: string
  ): Promise<void> => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/documents/download-multiple`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("cognito_id_token")}`,
          },
          body: JSON.stringify({ documentIds }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to download documents: ${response.statusText}`);
      }

      // Get filename from response headers or use provided name or default
      const contentDisposition = response.headers.get("content-disposition");
      let filename = zipFileName || `documents-${Date.now()}.zip`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        );
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, "");
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading multiple documents:", error);
      throw error;
    }
  },

  /**
   * Get document metadata by ID
   */
  getDocumentById: async (documentId: string): Promise<Document | null> => {
    try {
      const response = await apiClient.get<Document>(
        `/documents/${documentId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching document:", error);
      return null;
    }
  },

  /**
   * Get multiple documents metadata by IDs
   */
  getDocumentsByIds: async (documentIds: string[]): Promise<Document[]> => {
    try {
      const response = await apiClient.post<Document[]>("/documents/by-ids", {
        documentIds,
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching documents:", error);
      return [];
    }
  },
};
