/**
 * User role types for authorization
 */
export type UserRole =
  | "admin"
  | "applicant"
  | "Platform Admin"
  | "Scheme Admin"
  | "Scheme Admin Junior"
  | "PLATEFORM_ADMIN"
  | "SCHEME_ADMIN"
  | "SCHEME_ADMIN_JUNIOR"
  | "Assessor"
  | "assessor"
  | "delivery partner"
  | "delivery_partner";

/**
 * User interface for authentication
 */
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
}

/**
 * Login credentials interface
 */
export interface LoginCredentials {
  email: string;
  password: string;
}

/**
 * Authentication state interface
 */
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

/**
 * Authentication context interface
 */
export interface AuthContextType extends AuthState {
  login: () => void;
  logout: () => Promise<void>;
  hasPermission: (requiredRole: UserRole) => boolean;
  handleCallback: (code: string) => Promise<boolean>;
}
