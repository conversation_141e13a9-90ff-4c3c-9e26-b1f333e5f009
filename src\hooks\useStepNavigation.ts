import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { FormComponent, FormSchema } from "@/lib/types/form";

interface UseStepNavigationProps {
  steps: {
    id: string;
    label: string;
    description?: string;
    icon?: string;
    components: FormComponent[];
  }[];
  methods: UseFormReturn;
  schema?: FormSchema;
  setFormStatus: React.Dispatch<
    React.SetStateAction<{
      isSubmitted: boolean;
      isValid: boolean;
      message: string;
    }>
  >;
  saveProgress?: () => Promise<boolean>;
}

interface UseStepNavigationReturn {
  currentStep: number;
  currentStepData: {
    id: string;
    label: string;
    description?: string;
    icon?: string;
    components: FormComponent[];
  };
  nextStep: () => Promise<void>;
  prevStep: () => void;
  commitCurrentStepData: () => Promise<boolean>;
}

/**
 * Custom hook for multi-step form navigation
 */
export function useStepNavigation({
  steps,
  methods,
  setFormStatus,
  saveProgress,
}: UseStepNavigationProps): UseStepNavigationReturn {
  // State for multi-step form
  const [currentStep, setCurrentStep] = useState(0);
  const currentStepData = steps[currentStep] || {
    id: "",
    label: "",
    components: [],
  };
  const [isSaving, setIsSaving] = useState(false);

  /**
   * Commits the current step's form data to the form state
   * This ensures all field values are properly captured before validation or saving
   */
  const commitCurrentStepData = async (): Promise<boolean> => {
    try {
      const { setValue } = methods;

      // 1. Capture form field values from the DOM
      const formElement = document.querySelector("form");
      if (formElement) {
        const formData = new FormData(formElement);
        formData.forEach((value, key) => {
          const currentValue = methods.getValues(key);
          if (currentValue !== value) {
            setValue(key, value, { shouldDirty: true, shouldTouch: true });
          }
        });
      }

      // 2. Special handling for DataGrid components
      if (currentStepData?.components) {
        const dataGridComponents = currentStepData.components.filter(
          (component) => component.type === "datagrid"
        );

        // Process each DataGrid component
        for (const component of dataGridComponents) {
          const componentName = component.name;
          const currentValue = methods.getValues(componentName);

          // Check if we need to update the DataGrid value
          if (
            !currentValue ||
            (typeof currentValue === "object" &&
              !currentValue.rows &&
              !currentValue.metadata &&
              !currentValue.flat)
          ) {
            // Try to find and update the DataGrid element
            const dataGridElement = document.querySelector(
              `[name="${componentName}"]`
            );

            if (dataGridElement) {
              try {
                // Force an update by triggering a change event
                const event = new Event("change", { bubbles: true });
                dataGridElement.dispatchEvent(event);

                // Give a small delay for the event to be processed
                await new Promise((resolve) => setTimeout(resolve, 50));
              } catch (error) {
                console.error(
                  `Error updating DataGrid ${componentName}:`,
                  error
                );
                // Continue despite error - we'll use whatever data we have
              }
            }
          }
        }
      }

      return true;
    } catch (error) {
      console.error("Error committing form data:", error);
      return false;
    }
  };

  /**
   * Gets all field names in the current step that should be validated
   */
  // const getCurrentStepFieldNames = () => {
  //   const { watch } = methods;
  //   const fieldNames: string[] = [];

  //   // Add direct component fields
  //   currentStepData.components
  //     .filter(
  //       (component) =>
  //         component.type !== "step" &&
  //         component.type !== "section" &&
  //         evaluateConditionalRendering(component, watch)
  //     )
  //     .forEach((component) => fieldNames.push(component.name));

  //   // Add fields in sections within this step
  //   currentStepData.components
  //     .filter(
  //       (component) =>
  //         component.type === "section" &&
  //         evaluateConditionalRendering(component, watch)
  //     )
  //     .forEach((section) => {
  //       const childComponents = schema.components.filter(
  //         (c) => c.parentId === section.id
  //       );

  //       childComponents
  //         .filter(
  //           (child) =>
  //             child.type !== "section" &&
  //             evaluateConditionalRendering(child, watch)
  //         )
  //         .forEach((child) => {
  //           fieldNames.push(child.name);
  //         });
  //     });

  //   return fieldNames;
  // };

  /**
   * Handles navigation to the next step
   * Allows free navigation without validation - validation only happens on final submit
   */
  const nextStep = async () => {
    // Prevent multiple clicks
    if (isSaving) return;

    try {
      setIsSaving(true);

      // 1. Commit current step data (save current values without validation)
      await commitCurrentStepData();

      // 2. Save progress automatically when moving between steps (without validation)
      if (currentStep < steps.length - 1) {
        // Show saving indicator
        setFormStatus({
          isSubmitted: true,
          isValid: true,
          message: "Saving progress...",
        });

        // Save progress without validation
        let saveSuccessful = true;
        if (saveProgress) {
          saveSuccessful = await saveProgress();
        }

        if (!saveSuccessful) {
          setFormStatus({
            isSubmitted: true,
            isValid: false,
            message: "Failed to save progress. Please try again.",
          });
          return;
        }

        // 3. Move to the next step
        setFormStatus({
          isSubmitted: false,
          isValid: false,
          message: "",
        });

        setCurrentStep(currentStep + 1);
      }
    } catch (error) {
      console.error("Error during next step navigation:", error);
      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: "An error occurred. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Handles navigation to the previous step
   */
  const prevStep = () => {
    if (currentStep > 0) {
      // Reset form status when moving to previous step
      setFormStatus({
        isSubmitted: false,
        isValid: false,
        message: "",
      });
      setCurrentStep(currentStep - 1);
    }
  };

  return {
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    commitCurrentStepData,
  };
}
