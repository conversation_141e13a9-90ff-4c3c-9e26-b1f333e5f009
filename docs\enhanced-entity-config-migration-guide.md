# Enhanced Entity Configuration Migration Guide

This guide explains how to migrate from the old hardcoded entity configuration system to the new generic, dependency-aware system.

## Overview of Changes

The new system provides:
- **Generic dynamic options** that can depend on other field values
- **Enhanced auto-fill** with configurable data transformation
- **Automatic caching** to reduce API calls
- **Field dependency management** with real-time updates
- **Backward compatibility** with existing configurations

## Migration Examples

### 1. Migrating Dynamic Options

#### Before (Old System)
```typescript
{
  id: "userId",
  name: "userId",
  type: "select",
  label: "User ID",
  dynamicOptions: async () => {
    const response = await apiClient.get(`/users?role=applicant`);
    return response.data.content.map(user => ({
      label: `${user.firstName} ${user.lastName}`,
      value: user.id.toString(),
    }));
  },
}
```

#### After (New System)
```typescript
{
  id: "userId",
  name: "userId",
  type: "select",
  label: "User ID",
  dynamicOptionsConfig: {
    resolver: async (context) => {
      const selectedRole = context.getFieldValue("role");
      const roleName = getRoleName(selectedRole); // Your mapping logic
      
      const response = await context.apiClient.get(`/users?role=${roleName}`);
      return response.data.content.map(user => ({
        label: `${user.firstName} ${user.lastName}`,
        value: user.id.toString(),
      }));
    },
    dependsOn: ["role"],
    clearOnEmptyDependency: true,
    cacheKey: (context) => `users-by-role-${context.getFieldValue("role")}`,
  },
}
```

### 2. Migrating Auto-Fill

#### Before (Old System)
```typescript
{
  id: "fundingRoundId",
  name: "fundingRoundId",
  type: "select",
  label: "Funding Round",
  autoFill: true,
  autoFillFn: async () => {
    const response = await apiClient.get("/fundingRounds/active");
    return response.data;
  },
}
```

#### After (New System)
```typescript
{
  id: "fundingRoundId",
  name: "fundingRoundId",
  type: "select",
  label: "Funding Round",
  autoFillConfig: {
    resolver: async (context) => {
      if (context.initialData?.fundingRoundId) {
        // Editing existing record
        const response = await context.apiClient.get(
          `/fundingRounds/${context.initialData.fundingRoundId}`
        );
        return response.data;
      } else {
        // Creating new record
        const response = await context.apiClient.get("/fundingRounds/active");
        return response.data;
      }
    },
    createOptions: true,
    optionsFormatter: (value) => [{
      label: `Round ${value.roundNumber} (${new Date(value.openDate).toLocaleDateString()})`,
      value: value.id,
    }],
    transform: (value) => value.id,
    onlyOnInitialLoad: true,
  },
}
```

## Key Benefits

### 1. Field Dependencies
Fields can now depend on other field values:
```typescript
dynamicOptionsConfig: {
  dependsOn: ["category", "subcategory"],
  resolver: async (context) => {
    const category = context.getFieldValue("category");
    const subcategory = context.getFieldValue("subcategory");
    // Use both values to fetch relevant options
  },
}
```

### 2. Automatic Caching
Reduce API calls with intelligent caching:
```typescript
dynamicOptionsConfig: {
  cacheKey: (context) => `items-${context.getFieldValue("category")}`,
  // Options are cached per category
}
```

### 3. Context-Aware Resolvers
Access to form state and initial data:
```typescript
resolver: async (context) => {
  // Access current form values
  const formValue = context.getFieldValue("someField");
  
  // Access initial/editing data
  const isEditing = !!context.initialData?.id;
  
  // Make API calls
  const response = await context.apiClient.get("/endpoint");
}
```

### 4. Flexible Data Transformation
Transform resolved data before using:
```typescript
autoFillConfig: {
  resolver: async (context) => {
    // Fetch complex object
    const response = await context.apiClient.get("/complex-data");
    return response.data;
  },
  transform: (data) => data.id, // Extract just the ID
  createOptions: true,
  optionsFormatter: (data) => [{
    label: data.displayName,
    value: data.id,
  }],
}
```

## Backward Compatibility

The new system maintains full backward compatibility:
- Existing `dynamicOptions` functions continue to work
- Existing `autoFill` and `autoFillFn` continue to work
- No breaking changes to existing configurations

## Migration Strategy

1. **Gradual Migration**: Migrate one entity at a time
2. **Test Thoroughly**: Ensure existing functionality works
3. **Add Dependencies**: Identify fields that should depend on others
4. **Optimize Caching**: Add cache keys for frequently accessed data
5. **Remove Hardcoded Logic**: Replace hardcoded conditions with generic resolvers

## Common Patterns

### Pattern 1: Dependent Dropdowns
```typescript
// Parent field
{
  id: "country",
  name: "country",
  type: "select",
  options: [/* static country options */],
}

// Dependent field
{
  id: "state",
  name: "state",
  type: "select",
  dynamicOptionsConfig: {
    resolver: async (context) => {
      const country = context.getFieldValue("country");
      const response = await context.apiClient.get(`/states?country=${country}`);
      return response.data.map(state => ({ label: state.name, value: state.id }));
    },
    dependsOn: ["country"],
    clearOnEmptyDependency: true,
  },
}
```

### Pattern 2: Conditional Auto-Fill
```typescript
{
  id: "defaultValue",
  name: "defaultValue",
  type: "text",
  autoFillConfig: {
    resolver: async (context) => {
      const type = context.getFieldValue("type");
      if (type === "premium") {
        return "Premium Default Value";
      }
      return "Standard Default Value";
    },
    triggers: ["type"], // Re-run when type changes
  },
}
```

## Testing Your Migration

1. **Test Static Fields**: Ensure non-dynamic fields work as before
2. **Test Dynamic Options**: Verify options update when dependencies change
3. **Test Auto-Fill**: Check auto-fill works on create and edit
4. **Test Caching**: Verify API calls are minimized
5. **Test Edge Cases**: Empty dependencies, API errors, etc.

## Need Help?

If you encounter issues during migration:
1. Check the console for error messages
2. Verify your resolver functions return the expected format
3. Ensure dependency field names match exactly
4. Test with simple cases first, then add complexity
