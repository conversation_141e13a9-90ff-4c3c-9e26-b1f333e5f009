import React, { Suspense, lazy } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/theme-provider";
import MainLayout from "@/components/layout/MainLayout";
import AuthLayout from "@/components/layout/AuthLayout";
import RootLayout from "@/components/layout/RootLayout";
import { Loading } from "@/components/ui/loading";
import { AuthProvider } from "@/contexts/AuthContext";
import RoleBasedRedirect from "@/components/auth/RoleBasedRedirect";
import entityInitializer from "@/lib/services/entity-initializer";
import { QueryProvider } from "@/lib/providers/query-provider";
import { Toaster } from "./hooks/use-toast";


// Lazy load pages for code splitting
// const FormListPage = lazy(() => import("@/pages/FormListPage"));
const UserListPage = lazy(() => import("@/pages/UserListPage"));
const UserProjectListPage = lazy(() => import("@/pages/UserProjectListPage"));
const UploadDocumentPage = lazy(() => import("@/pages/DocumentsPage"));
const FormsList = lazy(() => import("@/pages/FormsList"));
const FormBuilderPage = lazy(() => import("@/pages/FormBuilderPage"));
const ApplicationsPage = lazy(() => import("@/pages/ApplicationsPage"));
const FormSubmissionPage = lazy(() => import("@/pages/FormSubmissionPage"));
const SubmissionsDashboardPage = lazy(
  () => import("@/pages/SubmissionsDashboardPage")
);
const SubmissionDetailPage = lazy(() => import("@/pages/SubmissionDetailPage"));
const NotFoundPage = lazy(() => import("@/pages/NotFoundPage"));
const UnauthorizedPage = lazy(() => import("@/pages/UnauthorizedPage"));
const CallbackPage = lazy(() => import("@/pages/CallbackPage"));
const ProjectsList = lazy(() => import("@/pages/projects-list"));
const FundingRoundList = lazy(() => import("@/pages/FundingRound-list"));
const DynamicDetailsPage = lazy(() => import("@/pages/DynamicDetailsPage"));

function App() {
  React.useEffect(() => {
    // Clear any redirect flags
    if (sessionStorage.getItem("cognito_redirecting")) {
      sessionStorage.removeItem("cognito_redirecting");
    }

    // Initialize entity storage with sample data
    entityInitializer.initializeEntityStorage();
  }, []);

  return (
    <ThemeProvider defaultTheme="system" storageKey="hnes-form-builder-theme">
      <AuthProvider>
        <QueryProvider>
          <Router>
            <Suspense fallback={<Loading />}>
              <Routes>
                {/* Root layout that handles authentication check */}
                <Route element={<RootLayout />}>
                  {/* Auth routes with AuthLayout */}
                  <Route element={<AuthLayout />}>
                    <Route path="/callback" element={<CallbackPage />} />
                  </Route>

                  {/* Main application routes with MainLayout */}
                  <Route element={<MainLayout />}>
                    {/* Public routes */}
                    <Route
                      path="/unauthorized"
                      element={<UnauthorizedPage />}
                    />

                    {/* Root route with role-based redirect */}
                    <Route path="/" element={<RoleBasedRedirect />} />

                    {/* Routes for all authenticated users */}
                    <Route
                      path="/applications"
                      element={<ApplicationsPage />}
                    />
                    <Route
                      path="/applications/:formId/submit"
                      element={<FormSubmissionPage />}
                    />
                    <Route
                      path="/applications/:formId/submit/:projectRef"
                      element={<FormSubmissionPage />}
                    />
                    <Route
                      path="/applications/:formId/submissions/:projectRef"
                      element={<FormSubmissionPage />}
                    />

                    {/* Routes for admin users only */}
                    <Route path="/forms" element={<FormsList />} />
                    <Route path="/forms/new" element={<FormBuilderPage />} />
                    <Route path="/forms/form/:id" element={<FormBuilderPage />} />
                    <Route
                      path="/submissions"
                      element={<SubmissionsDashboardPage />}
                    />
                    <Route
                      path="/submissions/:id"
                      element={<SubmissionDetailPage />}
                    />
                    <Route path="/projects" element={<ProjectsList />} />
                    <Route
                      path="/funding-rounds"
                      element={<FundingRoundList />}
                    />
                    <Route
                      path="/projects/:projectId/documents"
                      element={<UploadDocumentPage />}
                    />
                    <Route
                      path="/projects/:entityName/:id"
                      element={<DynamicDetailsPage />}
                    />
                    <Route
                      path="/projects/:entityName/:id/users"
                      element={<UserProjectListPage />}
                    />
                    <Route
                      path="/funding-rounds/:entityName/:id"
                      element={<DynamicDetailsPage />}
                    />
                    <Route path="/users" element={<UserListPage />} />

                    <Route
                      path="/users/:entityName/:id"
                      element={<DynamicDetailsPage />}
                    />


                    {/* Fallback route */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Route>
                </Route>
              </Routes>
            </Suspense>
            <Toaster />
          </Router>
        </QueryProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
