/**
 * Example demonstrating the enhanced entity configuration system
 * with dynamic options that depend on other field values
 */

import { EntityConfig } from "@/lib/types/page-config";

export const ExampleEntityConfig: EntityConfig = {
  id: "example-entity",
  entityName: "ExampleEntity",
  title: "Example Entity",
  description: "Demonstrates enhanced field configuration",
  permissions: ["admin"],
  endpoints: {
    list: "/example",
    get: "/example",
    create: "/example",
    update: "/example",
    delete: "/example",
  },

  fields: [
    // Example 1: Auto-fill field with transformation
    {
      id: "autoFilledField",
      name: "autoFilledField",
      type: "text",
      label: "Auto-filled Field",
      disabled: true,
      autoFillConfig: {
        resolver: async (context) => {
          // Get data from API
          const response = await context.apiClient.get("/some-endpoint");
          return response.data;
        },
        transform: (value) => value.name || value.id,
        onlyOnInitialLoad: true,
      },
    },

    // Example 2: Category selection
    {
      id: "category",
      name: "category",
      type: "select",
      label: "Category",
      required: true,
      options: [
        { label: "Electronics", value: "electronics" },
        { label: "Clothing", value: "clothing" },
        { label: "Books", value: "books" },
      ],
    },

    // Example 3: Subcategory that depends on category
    {
      id: "subcategory",
      name: "subcategory",
      type: "select",
      label: "Subcategory",
      required: true,
      dynamicOptionsConfig: {
        resolver: async (context) => {
          const category = context.getFieldValue("category");
          
          // Define subcategories for each category
          const subcategories: Record<string, { label: string; value: string }[]> = {
            electronics: [
              { label: "Smartphones", value: "smartphones" },
              { label: "Laptops", value: "laptops" },
              { label: "Tablets", value: "tablets" },
            ],
            clothing: [
              { label: "Shirts", value: "shirts" },
              { label: "Pants", value: "pants" },
              { label: "Shoes", value: "shoes" },
            ],
            books: [
              { label: "Fiction", value: "fiction" },
              { label: "Non-fiction", value: "non-fiction" },
              { label: "Technical", value: "technical" },
            ],
          };

          return subcategories[category] || [];
        },
        dependsOn: ["category"],
        clearOnEmptyDependency: true,
        defaultOptions: [],
        cacheKey: (context) => `subcategories-${context.getFieldValue("category")}`,
      },
    },

    // Example 4: Dynamic options from API based on multiple dependencies
    {
      id: "relatedItems",
      name: "relatedItems",
      type: "select",
      label: "Related Items",
      dynamicOptionsConfig: {
        resolver: async (context) => {
          const category = context.getFieldValue("category");
          const subcategory = context.getFieldValue("subcategory");
          
          if (!category || !subcategory) {
            return [];
          }

          try {
            const response = await context.apiClient.get(
              `/items?category=${category}&subcategory=${subcategory}`
            );
            
            return response.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          } catch (error) {
            console.error("Error fetching related items:", error);
            return [];
          }
        },
        dependsOn: ["category", "subcategory"],
        clearOnEmptyDependency: true,
        defaultOptions: [{ label: "Select category and subcategory first", value: "" }],
        cacheKey: (context) => 
          `related-items-${context.getFieldValue("category")}-${context.getFieldValue("subcategory")}`,
      },
    },

    // Example 5: Auto-fill with options creation (like the funding round example)
    {
      id: "activePromotion",
      name: "activePromotion",
      type: "select",
      label: "Active Promotion",
      disabled: true,
      autoFillConfig: {
        resolver: async (context) => {
          // Check if editing existing data
          if (context.initialData?.promotionId) {
            const response = await context.apiClient.get(
              `/promotions/${context.initialData.promotionId}`
            );
            return response.data;
          } else {
            // Get active promotion for new records
            const response = await context.apiClient.get("/promotions/active");
            return response.data;
          }
        },
        createOptions: true,
        optionsFormatter: (promotion) => [{
          label: `${promotion.name} (${promotion.discount}% off)`,
          value: promotion.id,
        }],
        transform: (promotion) => promotion.id,
        onlyOnInitialLoad: true,
      },
    },

    // Example 6: Regular field for comparison
    {
      id: "description",
      name: "description",
      type: "text",
      label: "Description",
      placeholder: "Enter description...",
    },
  ],

  listConfig: {
    columns: [
      {
        id: "category",
        header: "Category",
        accessorKey: "category",
        type: "text",
        enableSorting: true,
        enableColumnFilter: true,
        width: 150,
      },
      {
        id: "subcategory",
        header: "Subcategory",
        accessorKey: "subcategory",
        type: "text",
        enableSorting: true,
        enableColumnFilter: true,
        width: 150,
      },
      {
        id: "description",
        header: "Description",
        accessorKey: "description",
        type: "text",
        enableSorting: false,
        enableColumnFilter: false,
        width: 200,
      },
    ],
    actions: [
      { id: "view", label: "View", icon: "eye", action: "view" },
      { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
      { id: "delete", label: "Delete", icon: "trash", action: "delete", requireConfirmation: true },
    ],
    defaultPageSize: 10,
    enableGlobalFilter: true,
    enableColumnFilters: true,
    enablePinning: false,
  },

  formConfig: {
    submitButtonText: "Save Example",
    cancelButtonText: "Cancel",
    successMessage: "Example saved successfully!",
    errorMessage: "Failed to save example.",
    redirectAfterSubmit: "/examples",
  },
};

/**
 * Usage Notes:
 * 
 * 1. The 'category' field is a regular select with static options
 * 
 * 2. The 'subcategory' field uses dynamicOptionsConfig to show different options
 *    based on the selected category. It will clear when category is empty.
 * 
 * 3. The 'relatedItems' field depends on both category and subcategory,
 *    making an API call to fetch relevant items.
 * 
 * 4. The 'activePromotion' field demonstrates auto-fill with option creation,
 *    similar to the funding round example but more generic.
 * 
 * 5. All dynamic fields include caching to avoid unnecessary API calls.
 * 
 * 6. The system maintains backward compatibility with existing configurations.
 */
