
import { CustomDialogProps } from '@/lib/types/page-config';
import { ProjectDocumentUploader } from './project-document-uploader';

/**
 * Wrapper component to make ProjectDocumentUploader compatible with CustomDialogProps
 * This bridges the gap between the existing uploader component and the new custom dialog system
 */
interface ProjectDocumentUploaderWrapperProps extends CustomDialogProps {
  // Additional props specific to document upload can be added here
}

export function ProjectDocumentUploaderWrapper({
  onSuccess,
  onCancel,
  contextData,
}: ProjectDocumentUploaderWrapperProps) {
  // Extract projectId from context data
  const projectId = contextData?.parentProjectId || contextData?.projectRef || '';

  // Handle successful upload
  const handleSuccess = () => {
    // Call the success callback to refresh the data and close the dialog
    onSuccess?.();
  };

  // Handle cancel
  const handleCancel = () => {
    // Call the cancel callback to close the dialog
    onCancel?.();
  };

  // Validate that we have a project ID
  if (!projectId) {
    return (
      <div className="p-6 text-center">
        <h3 className="text-lg font-semibold text-destructive mb-2">
          Missing Project Information
        </h3>
        <p className="text-muted-foreground mb-4">
          Unable to upload documents: Project ID is required but not provided.
        </p>
        <button
          onClick={handleCancel}
          className="px-4 py-2 bg-muted text-muted-foreground rounded hover:bg-muted/80"
        >
          Close
        </button>
      </div>
    );
  }

  return (
    <ProjectDocumentUploader
      projectId={projectId}
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  );
}
