import {
  FieldConfig,
  FieldContext,
  DynamicOptionsConfig,
  AutoFillConfig,
} from "@/lib/types/page-config";
import { apiClient } from "@/lib/api/api-client";

/**
 * Cache for dynamic options to avoid unnecessary API calls
 */
class OptionsCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  set(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

const optionsCache = new OptionsCache();

/**
 * Creates a field context object for dynamic resolvers
 */
export function createFieldContext(
  formValues: Record<string, any>,
  initialData?: Record<string, any>,
  fieldConfigs?: any[]
): FieldContext {
  return {
    formValues,
    initialData,
    apiClient,
    getFieldValue: (fieldName: string) => {
      // First check form values
      const formValue = formValues[fieldName];
      if (formValue !== undefined && formValue !== null && formValue !== "") {
        return formValue;
      }

      // If no form value, check for default value in field config
      if (fieldConfigs) {
        const fieldConfig = fieldConfigs.find((f: any) => f.name === fieldName);
        if (fieldConfig?.defaultValue !== undefined) {
          return fieldConfig.defaultValue;
        }
      }

      return formValue;
    },
    hasFieldValue: (fieldName: string) => {
      const value = formValues[fieldName];
      if (value !== undefined && value !== null && value !== "") {
        return true;
      }

      // Check if field has a default value
      if (fieldConfigs) {
        const fieldConfig = fieldConfigs.find((f: any) => f.name === fieldName);
        return fieldConfig?.defaultValue !== undefined;
      }

      return false;
    },
  };
}

/**
 * Resolves dynamic options for a field based on its configuration
 */
export async function resolveDynamicOptions(
  field: FieldConfig,
  context: FieldContext
): Promise<{ label: string; value: string }[]> {
  // Handle legacy dynamicOptions
  if (field.dynamicOptions && !field.dynamicOptionsConfig) {
    try {
      const options = await field.dynamicOptions();

      // Handle empty options array to prevent Select component errors
      if (!options || options.length === 0) {
        return [
          {
            label: "No options available",
            value: "no-options-available",
          },
        ];
      }

      // Validate that all options have non-empty values
      const validOptions = options.filter(
        (option) => option.value && option.value.trim() !== ""
      );

      if (validOptions.length === 0) {
        return [
          {
            label: "Invalid options data",
            value: "invalid-options-data",
          },
        ];
      }

      return validOptions;
    } catch (error) {
      console.error(
        `Error resolving legacy dynamic options for field ${field.id}:`,
        error
      );
      return [
        {
          label: "Error loading options - please try again",
          value: "error-loading-options",
        },
      ];
    }
  }

  // Handle new dynamicOptionsConfig
  if (field.dynamicOptionsConfig) {
    return await resolveDynamicOptionsConfig(
      field.dynamicOptionsConfig,
      context,
      field.id
    );
  }

  return field.options || [];
}

/**
 * Resolves dynamic options using the new configuration system
 */
async function resolveDynamicOptionsConfig(
  config: DynamicOptionsConfig,
  context: FieldContext,
  fieldId: string
): Promise<{ label: string; value: string }[]> {
  console.log("resolveDynamicOptionsConfig", config, context, fieldId);
  // Check if dependencies are met
  if (config.dependsOn && config.dependsOn.length > 0) {
    const missingDependencies = config.dependsOn.filter(
      (dep) => !context.hasFieldValue(dep)
    );
    console.log("🚀 ~ missingDependencies:", missingDependencies);

    if (missingDependencies.length > 0) {
      if (config.clearOnEmptyDependency) {
        return [];
      }
      return config.defaultOptions || [];
    }
  }

  // Generate cache key
  let cacheKey = "";
  if (config.cacheKey) {
    cacheKey = config.cacheKey(context);
  } else if (config.dependsOn) {
    const dependencyValues = config.dependsOn
      .map((dep) => `${dep}:${context.getFieldValue(dep)}`)
      .join("|");
    cacheKey = `${fieldId}:${dependencyValues}`;
  }

  // Check cache
  if (cacheKey) {
    const cached = optionsCache.get(cacheKey);
    if (cached) {
      return cached;
    }
  }

  try {
    // Resolve options
    const options = await config.resolver(context);
    console.log(`🚀 ~ Resolved options for field ${fieldId}:`, options);
    console.log(`🚀 ~ Context form values:`, context.formValues);

    // Handle empty options array to prevent Select component errors
    if (!options || options.length === 0) {
      const fallbackOptions = config.defaultOptions || [
        {
          label: "No options available",
          value: "no-options-available",
        },
      ];

      // Cache the fallback result
      if (cacheKey) {
        optionsCache.set(cacheKey, fallbackOptions);
      }

      return fallbackOptions;
    }

    // Validate that all options have non-empty values
    const validOptions = options.filter(
      (option) => option.value && option.value.trim() !== ""
    );

    if (validOptions.length === 0) {
      const fallbackOptions = [
        {
          label: "Invalid options data",
          value: "invalid-options-data",
        },
      ];

      // Cache the fallback result
      if (cacheKey) {
        optionsCache.set(cacheKey, fallbackOptions);
      }

      return fallbackOptions;
    }

    // Cache the valid result
    if (cacheKey) {
      optionsCache.set(cacheKey, validOptions);
    }

    return validOptions;
  } catch (error) {
    console.error(
      `Error resolving dynamic options for field ${fieldId}:`,
      error
    );
    const errorOptions = [
      {
        label: "Error loading options - please try again",
        value: "error-loading-options",
      },
    ];

    // Cache the error result briefly (shorter TTL)
    if (cacheKey) {
      optionsCache.set(cacheKey, errorOptions);
      // Clear error cache after 30 seconds to allow retry
      setTimeout(() => {
        optionsCache.delete(cacheKey);
      }, 30000);
    }

    return errorOptions;
  }
}

/**
 * Resolves auto-fill value for a field based on its configuration
 */
export async function resolveAutoFill(
  field: FieldConfig,
  context: FieldContext
): Promise<{
  value: any;
  options?: { label: string; value: string }[];
}> {
  // Handle legacy autoFill
  if (field.autoFill && field.autoFillFn && !field.autoFillConfig) {
    try {
      const value = await field.autoFillFn();
      return { value };
    } catch (error) {
      console.error(
        `Error resolving legacy auto-fill for field ${field.id}:`,
        error
      );
      return { value: null };
    }
  }

  // Handle new autoFillConfig
  if (field.autoFillConfig) {
    return await resolveAutoFillConfig(field.autoFillConfig, context, field.id);
  }

  return { value: field.defaultValue };
}

/**
 * Resolves auto-fill using the new configuration system
 */
async function resolveAutoFillConfig(
  config: AutoFillConfig,
  context: FieldContext,
  fieldId: string
): Promise<{
  value: any;
  options?: { label: string; value: string }[];
}> {
  try {
    // Resolve the raw value
    let value = await config.resolver(context);
    let transformedValue;
    console.log("🚀 ~ value:", value);

    // Apply transformation if provided
    if (config.transform) {
      transformedValue = config.transform(value, context);
      console.log("🚀 ~ value:", value);
    }

    // Create options if needed
    let options: { label: string; value: string }[] | undefined;
    if (config.createOptions && value) {
      if (config.optionsFormatter) {
        options = config.optionsFormatter(value, context);
        console.log("🚀 ~ options:", options);
      } else {
        // Default options formatting for common cases
        if (Array.isArray(value)) {
          options = value.map((item) => ({
            label: item.label || item.name || String(item),
            value: item.value || item.id || String(item),
          }));
        } else if (typeof value === "object" && value !== null) {
          options = [
            {
              label: value.label || value.name || String(value),
              value: value.value || value.id || String(value),
            },
          ];
        }
      }
    }

    return { value: transformedValue || value, options };
  } catch (error) {
    console.error(`Error resolving auto-fill for field ${fieldId}:`, error);
    return { value: null };
  }
}

/**
 * Checks if a field has dependencies that need to be resolved
 */
export function fieldHasDependencies(field: FieldConfig): boolean {
  return !!(
    field.dynamicOptionsConfig?.dependsOn?.length ||
    field.autoFillConfig?.triggers?.length
  );
}

/**
 * Gets all dependencies for a field
 */
export function getFieldDependencies(field: FieldConfig): string[] {
  const dependencies: string[] = [];

  if (field.dynamicOptionsConfig?.dependsOn) {
    dependencies.push(...field.dynamicOptionsConfig.dependsOn);
  }

  if (field.autoFillConfig?.triggers) {
    dependencies.push(...field.autoFillConfig.triggers);
  }

  return [...new Set(dependencies)]; // Remove duplicates
}

/**
 * Clears cache for specific field or all fields
 */
export function clearOptionsCache(fieldId?: string): void {
  if (fieldId) {
    // Clear all cache entries that start with the fieldId
    const keysToDelete: string[] = [];
    optionsCache["cache"].forEach((_, key) => {
      if (key.startsWith(`${fieldId}:`)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach((key) => optionsCache.delete(key));
  } else {
    optionsCache.clear();
  }
}

/**
 * Special option values that should not be considered valid selections
 */
export const INVALID_OPTION_VALUES = [
  "no-options-available",
  "no-users-found",
  "invalid-options-data",
  "error-loading-options",
  "error-loading-users",
  "loading",
] as const;

/**
 * Checks if a field value is a valid selection (not a placeholder/error option)
 */
export function isValidOptionValue(value: string): boolean {
  return !INVALID_OPTION_VALUES.includes(value as any);
}

/**
 * Validates form data and returns fields with invalid option selections
 */
export function validateOptionSelections(
  formData: Record<string, any>
): string[] {
  const invalidFields: string[] = [];

  Object.entries(formData).forEach(([fieldName, value]) => {
    if (typeof value === "string" && !isValidOptionValue(value)) {
      invalidFields.push(fieldName);
    }
  });

  return invalidFields;
}
