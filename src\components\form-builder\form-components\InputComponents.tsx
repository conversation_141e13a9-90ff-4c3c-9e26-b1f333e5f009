import { memo } from "react";
import { Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Base input props that all input components share
interface BaseInputProps {
  id: string;
  name: string;
  register?: any;
  control?: any;
  validationRules: Record<string, any>;
  errors: any;
  disabled?: boolean;
  defaultValue?: any;
}

// Props for text-based inputs
interface TextBasedInputProps extends BaseInputProps {
  placeholder?: string;
}

// Props for number inputs
interface NumberInputProps extends TextBasedInputProps {
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
}

// Props for date inputs
interface DateInputProps extends BaseInputProps {
  min?: string;
  max?: string;
}

// Props for select and option-based inputs
interface OptionBasedInputProps extends BaseInputProps {
  placeholder?: string;
  options: { label: string; value: string }[];
  defaultValue?: any;
}

/**
 * Text Input Component
 */
export const TextInput = memo(
  ({
    id,
    name,
    placeholder,
    disabled,
    defaultValue,
    register,
    validationRules,
    errors,
  }: TextBasedInputProps) => {
    if (disabled && !defaultValue) {
      return null
    }
    return (
      <Input
        id={id}
        placeholder={placeholder}
        aria-invalid={errors[name] ? "true" : "false"}
        disabled={disabled}
        defaultValue={defaultValue}

        {...register(name, validationRules)}
      />
    )
  }
);

TextInput.displayName = "TextInput";

/**
 * Number Input Component
 */
export const NumberInput = memo(
  ({
    id,
    name,
    placeholder,
    min,
    max,
    step,
    unit,
    register,
    validationRules,
    errors,
  }: NumberInputProps) => (
    <div className="relative">
      <Input
        id={id}
        type="number"
        placeholder={placeholder}
        min={min}
        max={max}
        step={step}
        aria-invalid={errors[name] ? "true" : "false"}
        className={unit ? "pr-8" : ""}
        {...register(name, {
          ...validationRules,
          valueAsNumber: true,
        })}
      />
      {unit && (
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
          <span className="text-sm text-muted-foreground">{unit}</span>
        </div>
      )}
    </div>
  )
);

NumberInput.displayName = "NumberInput";

/**
 * Date Input Component
 */
export const DateInput = memo(
  ({
    id,
    name,
    min,
    max,
    register,
    validationRules,
    errors,
  }: DateInputProps) => (
    <Input
      id={id}
      type="date"
      min={min}
      max={max}
      aria-invalid={errors[name] ? "true" : "false"}
      {...register(name, validationRules)}
    />
  )
);

DateInput.displayName = "DateInput";

/**
 * DateTime Input Component
 */
export const DateTimeInput = memo(
  ({
    id,
    name,
    min,
    max,
    register,
    validationRules,
    errors,
  }: DateInputProps) => (
    <Input
      id={id}
      type="datetime-local"
      min={min}
      max={max}
      aria-invalid={errors[name] ? "true" : "false"}
      {...register(name, validationRules)}
    />
  )
);

DateTimeInput.displayName = "DateTimeInput";

/**
 * Select Input Component
 */
export const SelectInput = memo(
  ({
    id,
    name,
    placeholder,
    options,
    control,
    validationRules,
    errors,
    defaultValue,
  }: OptionBasedInputProps) => (
    <Controller
      name={name}
      control={control}
      rules={validationRules}
      defaultValue={defaultValue}
      render={({ field }) => {
        // Debug the field value
        const isStatusField = name === "status";

        // Determine which value to use - prioritize field.value (current form value) over defaultValue
        const valueToUse =
          field.value !== undefined && field.value !== null && field.value !== ""
            ? field.value
            : defaultValue;

        // Convert value to string for the Select component
        let stringValue = "";

        // Handle the value conversion regardless of field type
        if (valueToUse !== undefined && valueToUse !== null) {
          // For object values
          if (typeof valueToUse === "object" && valueToUse !== null) {
            // Try to find a matching option
            const matchingOption = options.find(
              (opt) => JSON.stringify(opt.value) === JSON.stringify(valueToUse)
            );
            if (matchingOption) {
              stringValue = String(matchingOption.value);
            } else {
              // If no exact match, try to extract a value property if it exists
              stringValue =
                valueToUse.value || valueToUse.id || String(valueToUse);
            }
          } else {
            // For primitive values, convert to string
            stringValue = String(valueToUse);

            // Verify this value exists in options
            const matchingOption = options.find(
              (opt) => String(opt.value) === stringValue
            );

            if (matchingOption) {
            } else {
            }
          }
        }

        // Ensure the field value is set correctly
        if (valueToUse !== undefined && field.value !== valueToUse) {
          field.onChange(valueToUse);
        }
        // Handle value change to ensure proper type conversion
        const handleValueChange = (newValue: string) => {
          // Find the matching option to preserve its original type
          const selectedOption = options.find(
            (opt) => String(opt.value) === newValue
          );

          if (selectedOption) {
            // Use the original option value to preserve its type
            field.onChange(selectedOption.value);
          } else {
            // Try to convert to the appropriate type based on the current field value
            if (typeof field.value === "number") {
              const numValue = Number(newValue);
              field.onChange(numValue);
            } else {
              // Fallback to the string value
              field.onChange(newValue);
            }
          }
        };

        // Add a key to force re-render when value changes for all select fields
        const selectKey = `${name}-select-${stringValue}-${JSON.stringify(options).slice(0, 50)}`;

        return (
          <Select
            key={selectKey}
            onValueChange={handleValueChange}
            value={stringValue}
            defaultValue={stringValue}
          >
            <SelectTrigger
              id={id}
              aria-invalid={errors[name] ? "true" : "false"}
              className={errors[name] ? "border-destructive" : ""}
            >
              <SelectValue placeholder={placeholder ?? "Select an option"} />
            </SelectTrigger>
            <SelectContent>
              {Array.isArray(options) && options.length > 0 ? (
                isStatusField ? (
                  // Special rendering for status options
                  <>
                    {options.map((option) => {
                      return (
                        <SelectItem
                          key={option.value}
                          value={String(option.value)}
                        >
                          {option.label}
                        </SelectItem>
                      );
                    })}
                  </>
                ) : (
                  // Standard rendering for other options
                  options.map((option) => (
                    <SelectItem
                      key={
                        typeof option.value === "object"
                          ? JSON.stringify(option.value)
                          : option.value
                      }
                      value={String(option.value)}
                    >
                      {option.label}
                    </SelectItem>
                  ))
                )
              ) : (
                <SelectItem value="" disabled>
                  No options available
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        );
      }}
    />
  )
);

SelectInput.displayName = "SelectInput";

/**
 * Checkbox Input Component (Single checkbox)
 */
export const CheckboxInput = memo(
  ({ id, name, control, validationRules, errors }: BaseInputProps) => (
    <div className="items-top flex space-x-2">
      <Controller
        name={name}
        control={control}
        rules={validationRules}
        render={({ field }) => (
          <Checkbox
            id={id}
            checked={field.value}
            onCheckedChange={field.onChange}
            aria-invalid={errors[name] ? "true" : "false"}
            className={errors[name] ? "border-destructive" : ""}
          />
        )}
      />
    </div>
  )
);

CheckboxInput.displayName = "CheckboxInput";

/**
 * Checkbox Group Input Component (Multiple checkboxes)
 */
export const CheckboxGroupInput = memo(
  ({
    id,
    name,
    options,
    control,
    validationRules,
    errors,
  }: OptionBasedInputProps) => (
    <Controller
      name={name}
      control={control}
      rules={validationRules}
      defaultValue={[]}
      render={({ field }) => {
        // Ensure field.value is always an array
        const values = Array.isArray(field.value) ? field.value : [];

        return (
          <div className="space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`${id}-${option.value}`}
                  checked={values.includes(option.value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      field.onChange([...values, option.value]);
                    } else {
                      field.onChange(
                        values.filter((value) => value !== option.value)
                      );
                    }
                  }}
                  aria-invalid={errors[name] ? "true" : "false"}
                  className={errors[name] ? "border-destructive" : ""}
                />
                <Label htmlFor={`${id}-${option.value}`}>{option.label}</Label>
              </div>
            ))}
          </div>
        );
      }}
    />
  )
);

CheckboxGroupInput.displayName = "CheckboxGroupInput";

/**
 * Radio Input Component
 */
export const RadioInput = memo(
  ({
    id,
    name,
    options,
    control,
    validationRules,
    errors,
  }: OptionBasedInputProps) => (
    <Controller
      name={name}
      control={control}
      rules={validationRules}
      render={({ field }) => (
        <RadioGroup
          onValueChange={field.onChange}
          value={field.value ?? ""}
          className={errors[name] ? "text-destructive" : ""}
        >
          {options.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem
                value={option.value}
                id={`${id}-${option.value}`}
                aria-invalid={errors[name] ? "true" : "false"}
                className={errors[name] ? "border-destructive" : ""}
              />
              <Label htmlFor={`${id}-${option.value}`}>{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
      )}
    />
  )
);

RadioInput.displayName = "RadioInput";

/**
 * Props for InfoText component
 */
interface InfoTextInputProps {
  id: string;
  infoContent: string;
  variant?: "default" | "info" | "warning" | "success";
}

/**
 * InfoText Component - Displays informative text in a styled card/alert
 */
export const InfoTextInput = memo(
  ({ id, infoContent, variant = "default" }: InfoTextInputProps) => {
    // Define variant-specific styles
    const variantStyles = {
      default: "bg-muted text-foreground",
      info: "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-900 dark:text-blue-300",
      warning:
        "bg-amber-50 border-amber-200 text-amber-800 dark:bg-amber-950 dark:border-amber-900 dark:text-amber-300",
      success:
        "bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-900 dark:text-green-300",
    };

    return (
      <Alert
        id={id}
        className={cn("border p-4 rounded-md", variantStyles[variant])}
      >
        <div className="flex items-start">
          <InfoIcon className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <AlertDescription className="text-sm">{infoContent}</AlertDescription>
        </div>
      </Alert>
    );
  }
);

InfoTextInput.displayName = "InfoTextInput";
