import { useEntityParams, EntityParamsResult } from "@/hooks/useEntityParams";
import { ListPageConfig, ParameterConfig } from "@/lib/types/page-config";

/**
 * Enhanced props for DynamicPage with parameter support
 */
export interface EnhancedDynamicPageProps {
  config: ListPageConfig;
  entityId?: string;
  dynamicPath?: string;
  contextData?: Record<string, any>;
}

/**
 * Hook to prepare enhanced DynamicPage props with parameter extraction
 */
export function useEnhancedDynamicPageProps(
  config: ListPageConfig,
  entityId?: string
): EnhancedDynamicPageProps {
  const paramResult = useEntityParams(config.parameterConfig);

  // Transform context data if transformer is provided
  let contextData = paramResult.contextData;
  if (config.contextTransform && Object.keys(paramResult.params).length > 0) {
    try {
      const transformedData = config.contextTransform(
        paramResult.params as Record<string, string>
      );
      contextData = { ...contextData, ...transformedData };
    } catch (error) {
      console.warn("Error transforming context data:", error);
    }
  }

  return {
    config,
    entityId,
    dynamicPath: paramResult.dynamicPath,
    contextData,
  };
}

/**
 * Utility to validate that all required parameters are present
 */
export function validateRequiredParameters(
  params: Record<string, string | undefined>,
  required: string[]
): { isValid: boolean; missingParams: string[] } {
  const missingParams = required.filter((param) => !params[param]);

  return {
    isValid: missingParams.length === 0,
    missingParams,
  };
}

/**
 * Utility to build API endpoint with dynamic path
 */
export function buildApiEndpoint(
  baseEndpoint: string,
  dynamicPath: string
): string {
  // Remove trailing slash from base endpoint
  const cleanBase = baseEndpoint.replace(/\/$/, "");

  // Ensure dynamic path starts with slash if not empty
  const cleanDynamic =
    dynamicPath && !dynamicPath.startsWith("/")
      ? `/${dynamicPath}`
      : dynamicPath;

  return `${cleanBase}${cleanDynamic || ""}`;
}

/**
 * Utility to extract parameter values for form initialization
 */
export function extractFormInitialValues(
  contextData: Record<string, any>,
  fieldMappings?: Record<string, string>
): Record<string, any> {
  const initialValues: Record<string, any> = {};

  if (!fieldMappings) {
    // If no mappings provided, use context data as-is
    return contextData;
  }

  // Map context data to form fields using provided mappings
  for (const [contextKey, formField] of Object.entries(fieldMappings)) {
    if (contextData[contextKey] !== undefined) {
      initialValues[formField] = contextData[contextKey];
    }
  }

  return initialValues;
}

/**
 * Utility to determine if a page should show parameter validation errors
 */
export function shouldShowParameterErrors(
  paramResult: EntityParamsResult,
  config: ListPageConfig
): boolean {
  // Don't show errors if no parameter config is defined
  if (!config.parameterConfig) {
    return false;
  }

  // Show errors if there are validation errors and required parameters are configured
  return (
    !paramResult.isValid &&
    paramResult.errors.length > 0 &&
    (config.parameterConfig.required?.length || 0) > 0
  );
}

/**
 * Type guard to check if a config has parameter configuration
 */
export function hasParameterConfig(
  config: ListPageConfig
): config is ListPageConfig & { parameterConfig: ParameterConfig } {
  return config.parameterConfig !== undefined;
}

/**
 * Type guard to check if a config has custom dialogs
 */
export function hasCustomDialogs(
  config: ListPageConfig
): config is ListPageConfig & {
  customDialogs: NonNullable<ListPageConfig["customDialogs"]>;
} {
  return config.customDialogs !== undefined;
}
